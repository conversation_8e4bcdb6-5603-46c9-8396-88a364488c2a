package com.xpaas.zlkgl.service;

import com.xpaas.core.tool.api.R;
import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import com.xpaas.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.Map;

/**
 * 教学评价-资料库平台-我的收藏表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface IWdscService extends BaseService<Wdsc> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param map 教学评价-资料库平台-我的收藏表 参数
	 * <AUTHOR>
	 * @since 2025-08-04
	 * @return
	 */
	R<IPage<WdscVO>> selectWdscPage(IPage<WdscVO> page, Map<String, Object> map);

}
