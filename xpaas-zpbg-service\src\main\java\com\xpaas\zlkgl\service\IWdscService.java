package com.xpaas.zlkgl.service;

import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import com.xpaas.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 教学评价-资料库平台-我的收藏表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface IWdscService extends BaseService<Wdsc> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param wdsc 教学评价-资料库平台-我的收藏表 实体
	 * <AUTHOR>
	 * @since 2025-08-04
	 * @return
	 */
	IPage<WdscVO> selectWdscPage(IPage<WdscVO> page, WdscVO wdsc);

}
