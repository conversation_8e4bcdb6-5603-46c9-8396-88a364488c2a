package com.xpaas.zlkgl.service;

import com.xpaas.zlkgl.enums.FolderTypeEnum;

/**
 * 复制服务接口
 * 提供文件和文件夹的复制功能
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ICopyService {

    /**
     * 复制文件
     * 文件表是共用的，所以不需要区分文件夹类型
     *
     * @param fileId         源文件ID
     * @param targetFolderId 目标文件夹ID
     * @return 复制是否成功
     */
    boolean copyFile(String fileId, String targetFolderId);

    /**
     * 复制文件夹
     * 根据文件夹类型选择对应的复制策略
     *
     * @param folderId       源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @param folderType     文件夹类型
     * @return 复制是否成功
     */
    boolean copyFolder(String folderId, String targetFolderId, FolderTypeEnum folderType);

    /**
     * 复制分类管理文件夹
     *
     * @param folderId       源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @return 复制是否成功
     */
    boolean copyFlglFolder(String folderId, String targetFolderId);

    /**
     * 复制外校资料文件夹
     *
     * @param folderId       源文件夹ID
     * @param targetFolderId 目标父文件夹ID
     * @return 复制是否成功
     */
    boolean copyWxzlFolder(String folderId, String targetFolderId);
}
