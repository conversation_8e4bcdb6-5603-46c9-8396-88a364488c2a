package com.xpaas.core.permission.annotation;

import com.xpaas.core.permission.enums.OperationType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据权限控制注解 - 简化版
 * <p>
 * 实现简单的权限控制：
 * 1. 创建者可以删除/修改自己的数据
 * 2. 管理员可以删除/修改所有数据
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {

    /**
     * 实体类
     */
    Class<?> entityClass();

    /**
     * 操作类型
     */
    OperationType operation() default OperationType.MODIFY;

    /**
     * 权限检查失败时的错误消息
     */
    String message() default "无权限操作该数据";
}
