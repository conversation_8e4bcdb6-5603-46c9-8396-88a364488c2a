package com.xpaas.core.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 反射工具类，提供字段查找、字段值获取等通用反射操作。
 * <p>
 * 提供通用的反射和类型转换方法
 *
 * <AUTHOR>
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReflectionUtils {

    /**
     * 在类层次结构中查找字段（
     */
    private static Field findFieldInClassHierarchy(Class<?> clazz, String fieldName) {
        Class<?> currentClass = clazz;
        // 最多查找 4 层
        final int maxDepth = 4;
        int depth = 0;

        while (currentClass != null && depth < maxDepth) {
            try {
                return currentClass.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
                depth++;
            }
        }
        return null;
    }

    /**
     * 从对象中提取指定字段的值
     */
    public static Object getFieldValue(Object obj, String fieldName) {
        try {
            Field field = findFieldInClassHierarchy(obj.getClass(), fieldName);
            if (field != null) {
                field.setAccessible(true);
                return field.get(obj);
            }
        } catch (IllegalAccessException e) {
            log.error("访问对象[{}]的字段[{}]时权限不足", obj.getClass().getSimpleName(), fieldName, e);
        } catch (Exception e) {
            log.error("从对象[{}]中获取字段[{}]异常", obj.getClass().getSimpleName(), fieldName, e);
        }
        return null;
    }

    /**
     * 将参数值转换为ID列表
     */
    public static List<Long> convertToIdList(Object value) {
        if (value == null) {
            return Collections.emptyList();
        }

        List<Long> ids = new ArrayList<>();

        if (value instanceof List) {
            // List<?> 类型
            List<?> list = (List<?>) value;
            for (Object item : list) {
                Long id = convertToLong(item);
                if (id != null) {
                    ids.add(id);
                }
            }
        } else if (value instanceof Long[]) {
            // Long[] 数组
            Long[] array = (Long[]) value;
            ids.addAll(Arrays.asList(array));
        } else if (value instanceof String) {
            // 字符串类型（可能是单个ID或逗号分隔的多个ID）
            String str = (String) value;
            if (str.contains(",")) {
                // 逗号分隔的字符串
                String[] parts = str.split(",");
                for (String part : parts) {
                    Long id = convertToLong(part.trim());
                    if (id != null) {
                        ids.add(id);
                    }
                }
            } else {
                // 单个字符串ID
                Long id = convertToLong(str.trim());
                if (id != null) {
                    ids.add(id);
                }
            }
        } else {
            // 其他类型，尝试直接转换
            Long id = convertToLong(value);
            if (id != null) {
                ids.add(id);
            }
        }

        return ids;
    }

    /**
     * 转换为Long类型
     */
    private static Long convertToLong(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Long) {
            return (Long) value;
        }

        if (value instanceof Number) {
            return ((Number) value).longValue();
        }

        if (value instanceof String) {
            String str = ((String) value).trim();
            if (str.isEmpty()) {
                return null;
            }
            try {
                return Long.parseLong(str);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串[{}]转换为Long", str);
                return null;
            }
        }

        log.warn("不支持的ID类型: {}，值: {}", value.getClass().getSimpleName(), value);
        return null;
    }
}
