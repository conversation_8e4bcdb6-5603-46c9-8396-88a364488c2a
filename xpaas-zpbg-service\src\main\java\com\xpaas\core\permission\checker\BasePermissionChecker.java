package com.xpaas.core.permission.checker;

import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 权限检查器基类
 * <p>
 * 提供公共的权限检查方法：
 * 1. 管理员判断逻辑
 * 2. 当前用户获取
 * 3. 默认管理员判断
 *
 * <AUTHOR>
 * @since 2025/08/06
 */
@Slf4j
@Component
public abstract class BasePermissionChecker {

    /**
     * 管理员角色ID配置（从配置文件读取）
     */
    @Value("${xpaas.roles.adminRoleId:}")
    protected String adminRoleIds;

    @Value("${xpaas.roles.zlkGlyRoleId:}")
    protected String zlkGlyRoleId;

    /**
     * 获取当前登录用户
     */
    protected LoginUser getCurrentUser() {
        return AuthUtil.getUser();
    }

    /**
     * 判断当前用户是否为管理员
     */
    protected boolean isAdmin() {
        LoginUser currentUser = getCurrentUser();
        if (currentUser == null) {
            return false;
        }
        return isAdmin(currentUser.getRoleId());
    }

    /**
     * 判断角色是否为管理员
     */
    protected boolean isAdmin(String roleIds) {
        if (StringUtil.isBlank(roleIds)) {
            return false;
        }

        List<String> userRoles = Arrays.asList(roleIds.split(","));

        // 检查超级管理员
        if (StringUtil.isNotBlank(adminRoleIds)) {
            String[] adminRoleArray = adminRoleIds.split(",");
            for (String adminRole : adminRoleArray) {
                if (userRoles.contains(adminRole.trim())) {
                    return true;
                }
            }
        }

        // 检查教评中心管理员
        if (StringUtil.isNotBlank(zlkGlyRoleId)) {
            String[] zpbgAdminRoleArray = zlkGlyRoleId.split(",");
            for (String adminRole : zpbgAdminRoleArray) {
                if (userRoles.contains(adminRole.trim())) {
                    return true;
                }
            }
        }

        // 默认管理员判断逻辑（兜底）
        return isDefaultAdmin(roleIds);
    }

    /**
     * 默认管理员判断逻辑（当配置未加载时使用）
     */
    private boolean isDefaultAdmin(String roleIds) {
        LoginUser user = getCurrentUser();
        if (user != null) {
            // 检查特殊账号
            String account = user.getAccount();
            if ("admin".equals(account) || "admin_root".equals(account)) {
                return true;
            }
        }

        // 检查默认管理员角色ID
        List<String> userRoles = Arrays.asList(roleIds.split(","));
        return userRoles.contains("1") || userRoles.contains("admin");
    }
}
