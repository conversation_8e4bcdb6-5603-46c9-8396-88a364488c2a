package com.xpaas.zlkgl.enums;

import lombok.Getter;

/**
 * 文件夹类型枚举
 * 用于区分不同类型的文件夹操作
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Getter
public enum FolderTypeEnum {

    /**
     * 分类管理文件夹
     */
    FLGL("FLGL", "分类管理文件夹"),

    /**
     * 外校资料文件夹
     */
    WXZL("WXZL", "外校资料文件夹");

    private final String code;
    private final String description;

    FolderTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
