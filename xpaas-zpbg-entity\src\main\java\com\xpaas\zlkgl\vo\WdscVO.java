package com.xpaas.zlkgl.vo;

import com.xpaas.zlkgl.entity.Wdsc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 教学评价-资料库平台-我的收藏表视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WdscVO对象", description = "教学评价-资料库平台-我的收藏表")
public class WdscVO extends Wdsc {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

    @ApiModelProperty(value = "资料名称")
    private String zlMc;
    @ApiModelProperty(value = "资料重命名")
    private String zlCmm;
    @ApiModelProperty(value = "资料地址")
    private String zlDz;
    @ApiModelProperty(value = "外校资料类型")
    private String wxzlLx;
    @ApiModelProperty(value = "评价类型")
    private String pjLx;
    @ApiModelProperty(value = "资料密级")
    private String zlMj;
    @ApiModelProperty(value = "文件大小")
    private String wjDx;
    @ApiModelProperty(value = "文件格式")
    private String wjGs;
    @ApiModelProperty(value = "父级文件id")
    private String wjjId;
    @ApiModelProperty(value = "节点名称")
    private String jdMc;

}
