package com.xpaas.zlkgl.vo;

import com.xpaas.zlkgl.entity.Wdsc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 教学评价-资料库平台-我的收藏表视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WdscVO对象", description = "教学评价-资料库平台-我的收藏表")
public class WdscVO extends Wdsc {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
