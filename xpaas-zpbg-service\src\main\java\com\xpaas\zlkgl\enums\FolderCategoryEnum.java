package com.xpaas.zlkgl.enums;

import lombok.Getter;

/**
 * 文件夹类型枚举
 * 用于区分文件夹自身的不同类型
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Getter
public enum FolderCategoryEnum {

    // 外部链接
    OUTSIDE_LINK("0", "外部链接"),
    // 子文件夹
    SUB_FOLDER("1", "子文件夹");

    private final String code;
    private final String description;

    FolderCategoryEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据编码获取对应的枚举
    public static FolderCategoryEnum getByCode(String code) {
        for (FolderCategoryEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的文件夹类型编码: " + code);
    }

    // getter方法
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
