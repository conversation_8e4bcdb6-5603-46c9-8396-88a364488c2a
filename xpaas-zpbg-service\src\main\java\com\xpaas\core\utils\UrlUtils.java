package com.xpaas.core.utils;


import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.net.MalformedURLException;

/**
 * URL 工具类
 *
 * <AUTHOR>
 * @since 2025/08/01 13:32
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class UrlUtils {

    /**
     * 校验外部链接地址是否合法
     */
    public static boolean isValidHttpUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        url = url.trim();
        if (!(url.startsWith("http://") || url.startsWith("https://"))) {
            return false;
        }
        try {
            new java.net.URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
