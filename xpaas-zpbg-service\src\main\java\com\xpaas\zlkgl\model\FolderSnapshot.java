package com.xpaas.zlkgl.model;

import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 文件夹快照类
 * 用于在复制操作前保存文件夹结构，避免复制过程中的时序问题
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Data
public class FolderSnapshot {

    /**
     * 文件夹ID到子文件夹列表的映射
     * Key: 父文件夹ID
     * Value: 子文件夹对象列表
     */
    private Map<String, List<Object>> subFoldersMap;

    /**
     * 文件夹ID到文件列表的映射
     * Key: 父文件夹ID
     * Value: 文件对象列表
     */
    private Map<String, List<Object>> filesMap;

    /**
     * 根文件夹ID
     */
    private String rootFolderId;

    /**
     * 获取指定文件夹的子文件夹列表
     *
     * @param folderId 文件夹ID
     * @return 子文件夹列表，如果没有则返回空列表
     */
    public List<Object> getSubFolders(String folderId) {
        return subFoldersMap.getOrDefault(folderId, Collections.emptyList());
    }

    /**
     * 获取指定文件夹的文件列表
     *
     * @param folderId 文件夹ID
     * @return 文件列表，如果没有则返回空列表
     */
    public List<Object> getFiles(String folderId) {
        return filesMap.getOrDefault(folderId, Collections.emptyList());
    }
}
