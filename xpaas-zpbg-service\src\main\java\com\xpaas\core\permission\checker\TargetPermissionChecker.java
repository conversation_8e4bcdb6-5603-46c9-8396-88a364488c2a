package com.xpaas.core.permission.checker;

import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.permission.enums.TargetOperationType;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.tool.utils.SpringUtil;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.mapper.FlglWjjMapper;
import com.xpaas.zlkgl.mapper.WxzlWjjMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 目标位置权限检查器
 * <p>
 * 实现目标位置权限控制：
 * 1. 文件夹创建、移动、复制时检查目标文件夹权限
 * 2. 文件创建、移动、复制时检查目标文件夹权限
 * 3. 根目录不校验，管理员不校验
 *
 * <AUTHOR>
 * @since 2025/08/06
 */
@Slf4j
@Component
public class TargetPermissionChecker extends BasePermissionChecker {

    /**
     * 校验文件夹操作权限（创建、移动、复制）
     * 
     * @param targetFolderId 目标文件夹ID
     * @param folderType 文件夹类型（flgl_wjj/wxzl_wjj）
     * @param operation 操作类型
     */
    public void validateFolderOperation(String targetFolderId, String folderType, TargetOperationType operation) {
        try {

            // 1. 根目录不校验
            if (StringUtil.isBlank(targetFolderId)) {
                return;
            }
            
            // 2. 管理员不校验
            if (isAdmin()) {
                return;
            }
            
            // 3. 校验目标文件夹是否为当前用户创建
            if (!isTargetFolderCreatedByCurrentUser(targetFolderId, folderType)) {
                throw new ServiceException("无权限在此文件夹下进行" + operation.getDescription() + "操作");
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验文件夹操作权限异常", e);
            throw new ServiceException("权限校验失败");
        }
    }
    
    /**
     * 校验文件操作权限（创建、移动、复制）
     * 
     * @param targetFolderId 目标文件夹ID
     * @param operation 操作类型
     */
    public void validateFileOperation(String targetFolderId, TargetOperationType operation) {
        try {

            // 1. 文件必须挂靠文件夹
            if (StringUtil.isBlank(targetFolderId)) {
                throw new ServiceException("文件必须挂靠文件夹");
            }
            
            // 2. 管理员不校验
            if (isAdmin()) {
                return;
            }
            
            // 3. 校验目标文件夹权限（需要判断文件夹类型）
            String folderType = determineFolderType(targetFolderId);
            if (!isTargetFolderCreatedByCurrentUser(targetFolderId, folderType)) {
                throw new ServiceException("无权限在此文件夹下进行" + operation.getDescription() + "操作");
            }
            
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验文件操作权限异常", e);
            throw new ServiceException("权限校验失败");
        }
    }
    
    /**
     * 判断目标文件夹是否为当前用户创建
     */
    private boolean isTargetFolderCreatedByCurrentUser(String folderId, String folderType) {
        LoginUser currentUser = getCurrentUser();
        if (currentUser == null) {
            return false;
        }

        // 获取文件夹创建者ID
        Long creatorId = getFolderCreator(folderId, folderType);
        if (creatorId == null) {
            return false;
        }

        return Objects.equals(creatorId, currentUser.getUserId());
    }
    
    /**
     * 获取文件夹创建者ID
     */
    private Long getFolderCreator(String folderId, String folderType) {
        try {
            if ("flgl_wjj".equals(folderType)) {
                FlglWjjMapper mapper = SpringUtil.getBean(FlglWjjMapper.class);
                FlglWjj folder = mapper.selectById(folderId);
                return folder != null ? folder.getCjr() : null;
            } else if ("wxzl_wjj".equals(folderType)) {
                WxzlWjjMapper mapper = SpringUtil.getBean(WxzlWjjMapper.class);
                WxzlWjj folder = mapper.selectById(folderId);
                return folder != null ? folder.getCjr() : null;
            }
            
            return null;
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 判断文件夹类型
     */
    private String determineFolderType(String folderId) {
        try {
            // 先查FlglWjj
            FlglWjjMapper flglWjjMapper = SpringUtil.getBean(FlglWjjMapper.class);
            FlglWjj flglWjj = flglWjjMapper.selectById(folderId);
            if (flglWjj != null) {
                return "flgl_wjj";
            }
            
            // 再查WxzlWjj
            WxzlWjjMapper wxzlWjjMapper = SpringUtil.getBean(WxzlWjjMapper.class);
            WxzlWjj wxzlWjj = wxzlWjjMapper.selectById(folderId);
            if (wxzlWjj != null) {
                return "wxzl_wjj";
            }
            
            throw new ServiceException("目标文件夹不存在");
            
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("判断文件夹类型异常，文件夹ID: {}", folderId, e);
            throw new ServiceException("无法确定文件夹类型");
        }
    }
}
