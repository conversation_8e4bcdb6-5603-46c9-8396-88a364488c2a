package com.xpaas.core.permission.checker;

import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.permission.dto.FolderInfo;
import com.xpaas.core.permission.enums.FolderType;
import com.xpaas.core.permission.enums.TargetOperationType;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.tool.utils.SpringUtil;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.mapper.FlglWjjMapper;
import com.xpaas.zlkgl.mapper.WxzlWjjMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 目标位置权限检查器
 * <p>
 * 实现目标位置权限控制：
 * 1. 文件夹创建、移动、复制时检查目标文件夹权限
 * 2. 文件创建、移动、复制时检查目标文件夹权限
 * 3. 根目录不校验，管理员不校验
 * 4. 使用SpringUtil.getBean()保持组件独立性
 *
 * <AUTHOR>
 * @since 2025/08/06
 */
@Slf4j
@Component
public class TargetPermissionChecker extends BasePermissionChecker {

    /**
     * 根据指定类型获取文件夹信息
     */
    private static FolderInfo getFolderInfoByClass(String folderId, Class<?> folderClass) {
        try {
            if (FlglWjj.class.equals(folderClass)) {
                FlglWjjMapper mapper = SpringUtil.getBean(FlglWjjMapper.class);
                FlglWjj folder = mapper.selectById(folderId);
                return folder != null ?
                    new FolderInfo(FolderType.FLGL_WJJ, folder.getCjr()) :
                    FolderInfo.notFound();
            } else if (WxzlWjj.class.equals(folderClass)) {
                WxzlWjjMapper mapper = SpringUtil.getBean(WxzlWjjMapper.class);
                WxzlWjj folder = mapper.selectById(folderId);
                return folder != null ?
                    new FolderInfo(FolderType.WXZL_WJJ, folder.getCjr()) :
                    FolderInfo.notFound();
            }

            throw new ServiceException("不支持的文件夹类型: " + folderClass.getSimpleName());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取文件夹信息异常，文件夹ID: {}, 类型: {}", folderId, folderClass.getSimpleName(), e);
            return FolderInfo.notFound();
        }
    }

    /**
     * 自动判断文件夹类型并获取信息
     */
    private static FolderInfo getFolderInfo(String folderId) {
        try {
            // 先查FlglWjj
            FlglWjjMapper flglWjjMapper = SpringUtil.getBean(FlglWjjMapper.class);
            FlglWjj flglWjj = flglWjjMapper.selectById(folderId);
            if (flglWjj != null) {
                return new FolderInfo(FolderType.FLGL_WJJ, flglWjj.getCjr());
            }

            // 再查WxzlWjj
            WxzlWjjMapper wxzlWjjMapper = SpringUtil.getBean(WxzlWjjMapper.class);
            WxzlWjj wxzlWjj = wxzlWjjMapper.selectById(folderId);
            if (wxzlWjj != null) {
                return new FolderInfo(FolderType.WXZL_WJJ, wxzlWjj.getCjr());
            }

            return FolderInfo.notFound();

        } catch (Exception e) {
            log.error("获取文件夹信息异常，文件夹ID: {}", folderId, e);
            return FolderInfo.notFound();
        }
    }

    /**
     * 校验文件夹操作权限（创建、移动、复制）
     *
     * @param targetFolderId 目标文件夹ID
     * @param folderClass 文件夹类型Class
     * @param operation 操作类型
     */
    public void validateFolderOperation(String targetFolderId, Class<?> folderClass, TargetOperationType operation) {
        try {

            // 1. 根目录不校验
            if (StringUtil.isBlank(targetFolderId)) {
                return;
            }

            // 2. 管理员不校验
            if (isAdmin()) {
                return;
            }

            // 3. 一次查询获取文件夹信息
            FolderInfo folderInfo = getFolderInfoByClass(targetFolderId, folderClass);
            if (!folderInfo.isExists()) {
                throw new ServiceException("目标文件夹不存在");
            }

            // 4. 校验创建者
            if (!isCurrentUserCreator(folderInfo.getCreatorId())) {
                throw new ServiceException("无权限在此文件夹下进行" + operation.getDescription() + "操作");
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验文件夹操作权限异常", e);
            throw new ServiceException("权限校验失败");
        }
    }

    /**
     * 校验文件操作权限（创建、移动、复制）
     *
     * @param targetFolderId 目标文件夹ID
     * @param operation 操作类型
     */
    public void validateFileOperation(String targetFolderId, TargetOperationType operation) {
        try {

            // 1. 文件必须挂靠文件夹
            if (StringUtil.isBlank(targetFolderId)) {
                throw new ServiceException("文件必须挂靠文件夹");
            }

            // 2. 管理员不校验
            if (isAdmin()) {
                return;
            }

            // 3. 一次查询获取文件夹信息（自动判断类型）
            FolderInfo folderInfo = getFolderInfo(targetFolderId);

            if (!folderInfo.isExists()) {
                throw new ServiceException("目标文件夹不存在");
            }

            // 4. 校验创建者
            if (!isCurrentUserCreator(folderInfo.getCreatorId())) {
                throw new ServiceException("无权限在此文件夹下进行" + operation.getDescription() + "操作");
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验文件操作权限异常", e);
            throw new ServiceException("权限校验失败");
        }
    }

    /**
     * 判断是否为当前用户创建
     */
    private boolean isCurrentUserCreator(Long creatorId) {
        LoginUser currentUser = getCurrentUser();
        if (currentUser == null) {
            log.warn("未获取到当前登录用户信息");
            return false;
        }

        return Objects.equals(creatorId, currentUser.getUserId());
    }
}
