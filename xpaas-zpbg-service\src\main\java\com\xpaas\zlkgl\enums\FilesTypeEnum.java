package com.xpaas.zlkgl.enums;

/**
 * @program: xpaas-zpbg
 * @description: 资料类型区分 1：外校资料类型 0：非外校资料类型
 * @author: <PERSON>
 * @create: 2025-08-05 17:46
 */
public enum FilesTypeEnum {
    // 外校资料类型
    OUTSIDE_FILES("1", "外校资料类型"),
    // 子文件夹
    SUB_FILES("0", "非外校资料类型");

    private final String code;
    private final String description;

    FilesTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 根据编码获取对应的枚举
    public static FilesTypeEnum getByCode(String code) {
        for (FilesTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的文件夹类型编码: " + code);
    }

    // getter方法
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
