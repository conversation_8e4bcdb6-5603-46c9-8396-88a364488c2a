package com.xpaas.zlkgl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.mp.base.BaseService;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.vo.FlglWjjVO;

import java.util.List;
import java.util.Map;

/**
 * 教学评价-资料库平台-分类管理文件夹树表 服务类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface IFlglWjjService extends BaseService<FlglWjj> {

    /**
     * 自定义分页
     *
     * @param page
     * @param flglWjj 教学评价-资料库平台-分类管理文件夹树表 实体
     * @return
     * <AUTHOR>
     * @since 2025-07-24
     */
    IPage<FlglWjjVO> selectFlglWjjPage(IPage<FlglWjjVO> page, FlglWjjVO flglWjj);

    /**
     * 查询树结构
     */
    List<FlglWjjVO> listTree(FlglWjjVO bo);

    /**
     * 新增
     */
    boolean insert(FlglWjjVO vo);

    /**
     * 修改
     */
    boolean update(FlglWjjVO vo);

    /**
     * 删除
     */
    boolean deleteByIds(List<Long> ids);

    /**
     * 统计节点下的各个评价类型资源数（文件 + 链接）
     */
    List<Map<String, Object>> countResourcesByPjLx(String pjLx, String jdId);

    /**
     * 管理外部链接地址
     * 这是一个新增和修改通用的接口，主要管理外部链接地址
     *
     * @param vo 包含评价类型、节点ID、分类管理类别和外部链接地址的VO对象
     * @return 操作是否成功
     */
    boolean manageExternalLink(FlglWjjVO vo);

}
