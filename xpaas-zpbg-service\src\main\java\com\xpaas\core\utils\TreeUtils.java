package com.xpaas.core.utils;


import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 通用树形结构转换工具类
 *
 * <AUTHOR>
 * @since 2025/07/24
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TreeUtils {

    /**
     * 默认子节点字段名
     */
    private static final String DEFAULT_CHILDREN_FIELD = "children";

    /**
     * 将列表转换为树形结构（基本方法）
     *
     * @param list           需要转换的列表
     * @param idGetter       获取ID的函数
     * @param parentIdGetter 获取父ID的函数
     * @param <T>            实体类型
     * @param <I>            ID类型
     * @return 树形结构列表
     */
    public static <T, I> List<T> buildTree(List<T> list,
                                           Function<T, I> idGetter,
                                           Function<T, I> parentIdGetter) {
        return buildTree(list, idGetter, parentIdGetter,
                item -> {
                    I parentId = parentIdGetter.apply(item);
                    return parentId == null || StringUtils.isEmpty(parentId.toString());
                },
                DEFAULT_CHILDREN_FIELD, Function.identity());
    }

    /**
     * 将列表转换为树形结构（指定根节点条件）
     *
     * @param list           需要转换的列表
     * @param idGetter       获取ID的函数
     * @param parentIdGetter 获取父ID的函数
     * @param rootPredicate  根节点判断条件
     * @param <T>            实体类型
     * @param <I>            ID类型
     * @return 树形结构列表
     */
    public static <T, I> List<T> buildTree(List<T> list,
                                           Function<T, I> idGetter,
                                           Function<T, I> parentIdGetter,
                                           Predicate<T> rootPredicate) {
        return buildTree(list, idGetter, parentIdGetter,
                rootPredicate, DEFAULT_CHILDREN_FIELD, Function.identity());
    }

    /**
     * 将列表转换为树形结构（自定义子节点字段名）
     *
     * @param list              需要转换的列表
     * @param idGetter          获取ID的函数
     * @param parentIdGetter    获取父ID的函数
     * @param childrenFieldName 子节点列表字段名
     * @param <T>               实体类型
     * @param <I>               ID类型
     * @return 树形结构列表
     */
    public static <T, I> List<T> buildTree(List<T> list,
                                           Function<T, I> idGetter,
                                           Function<T, I> parentIdGetter,
                                           String childrenFieldName) {
        return buildTree(list, idGetter, parentIdGetter,
                item -> {
                    I parentId = parentIdGetter.apply(item);
                    return parentId == null || StringUtils.isEmpty(parentId.toString());
                },
                childrenFieldName, Function.identity());
    }

    /**
     * 将列表转换为树形结构（完整方法，支持所有功能）
     *
     * @param list              需要转换的列表
     * @param idGetter          获取ID的函数
     * @param parentIdGetter    获取父ID的函数
     * @param rootPredicate     根节点判断条件
     * @param childrenFieldName 子节点列表字段名
     * @param converter         节点转换函数
     * @param <T>               原实体类型
     * @param <R>               转换后实体类型
     * @param <I>               ID类型
     * @return 树形结构列表
     */
    private static <T, R, I> List<R> buildTree(List<T> list,
                                               Function<T, I> idGetter,
                                               Function<T, I> parentIdGetter,
                                               Predicate<T> rootPredicate,
                                               String childrenFieldName,
                                               Function<T, R> converter) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        if (idGetter == null || parentIdGetter == null || rootPredicate == null || childrenFieldName == null || converter == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 转换节点
        Map<I, R> convertedMap = new HashMap<>(list.size());
        Map<I, List<R>> parentChildrenMap = new HashMap<>(list.size());

        // 第一步：转换所有节点并按父ID分组
        for (T item : list) {
            I id = idGetter.apply(item);
            I parentId = parentIdGetter.apply(item);
            R convertedItem = converter.apply(item);

            convertedMap.put(id, convertedItem);

            if (parentId != null && !StringUtils.isEmpty(parentId.toString())) {
                List<R> children = parentChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>());
                children.add(convertedItem);
            }
        }

        // 第二步：构建树形结构
        List<R> result = new ArrayList<>();
        for (T item : list) {
            I id = idGetter.apply(item);
            R convertedItem = convertedMap.get(id);

            // 根据条件判断是否为根节点
            if (rootPredicate.test(item)) {
                result.add(convertedItem);
            }

            // 设置子节点
            List<R> children = parentChildrenMap.get(id);
            if (CollectionUtils.isNotEmpty(children)) {
                setChildrenByName(convertedItem, children, childrenFieldName);
            }
        }

        return result;
    }

    /**
     * 根据指定字段名设置子节点列表
     *
     * @param node              节点对象
     * @param children          子节点列表
     * @param childrenFieldName 子节点字段名
     * @param <R>               节点类型
     */
    private static <R> void setChildrenByName(R node, List<R> children, String childrenFieldName) {
        if (node == null || CollectionUtils.isEmpty(children) || StringUtils.isEmpty(childrenFieldName)) {
            return;
        }

        try {
            // 尝试使用setter方法
            String setterName = "set" + StringUtils.capitalize(childrenFieldName);
            try {
                Method setter = node.getClass().getMethod(setterName, List.class);
                setter.invoke(node, children);
            } catch (NoSuchMethodException e) {
                // 如果没有setter方法，尝试直接设置字段
                try {
                    Field field = node.getClass().getDeclaredField(childrenFieldName);
                    field.setAccessible(true);
                    field.set(node, children);
                } catch (NoSuchFieldException ex) {
                    throw new IllegalArgumentException("找不到字段或方法: " + childrenFieldName + " 或 " + setterName, ex);
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("设置子节点失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据指定字段名获取子节点列表
     *
     * @param node              节点对象
     * @param childrenFieldName 子节点字段名
     * @param <R>               节点类型
     * @return 子节点列表
     */
    @SuppressWarnings("unchecked")
    private static <R> List<R> getChildrenByName(R node, String childrenFieldName) {
        if (node == null || StringUtils.isEmpty(childrenFieldName)) {
            return Collections.emptyList();
        }

        try {
            // 尝试使用getter方法
            String getterName = "get" + StringUtils.capitalize(childrenFieldName);
            try {
                Method getter = node.getClass().getMethod(getterName);
                return (List<R>) getter.invoke(node);
            } catch (NoSuchMethodException e) {
                // 如果没有getter方法，尝试直接访问字段
                try {
                    Field field = node.getClass().getDeclaredField(childrenFieldName);
                    field.setAccessible(true);
                    return (List<R>) field.get(node);
                } catch (NoSuchFieldException ex) {
                    return Collections.emptyList();
                }
            }
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 排序树形结构
     *
     * @param treeList          树形结构列表
     * @param childrenFieldName 子节点字段名
     * @param comparator        比较器
     * @param <T>               实体类型
     */
    private static <T> void sortTree(List<T> treeList, String childrenFieldName, Comparator<T> comparator) {
        if (CollectionUtils.isEmpty(treeList) || comparator == null || StringUtils.isEmpty(childrenFieldName)) {
            return;
        }

        // 对当前级别排序
        treeList.sort(comparator);

        // 递归对子节点排序
        for (T node : treeList) {
            List<T> children = getChildrenByName(node, childrenFieldName);
            if (CollectionUtils.isNotEmpty(children)) {
                sortTree(children, childrenFieldName, comparator);
            }
        }
    }

    /**
     * 排序树形结构（使用默认子节点字段名"children"）
     *
     * @param treeList   树形结构列表
     * @param comparator 比较器
     * @param <T>        实体类型
     */
    public static <T> void sortTree(List<T> treeList, Comparator<T> comparator) {
        sortTree(treeList, DEFAULT_CHILDREN_FIELD, comparator);
    }

    /**
     * 排序树形结构
     *
     * @param treeList   树形结构列表
     * @param comparator 比较器
     * @param <T>        实体类型
     */
    private static <T> void sortTree(List<T> treeList, Comparator<T> comparator, String childrenFieldName) {
        sortTree(treeList, childrenFieldName, comparator);
    }

    /**
     * 扁平化树形结构
     *
     * @param treeList          树形结构列表
     * @param childrenFieldName 子节点字段名
     * @param clearChildren     是否清空子节点
     * @param <T>               实体类型
     * @return 扁平化后的列表
     */
    private static <T> List<T> flattenTree(List<T> treeList, String childrenFieldName, boolean clearChildren) {
        List<T> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(treeList) || StringUtils.isEmpty(childrenFieldName)) {
            return result;
        }

        for (T node : treeList) {
            result.add(node);
            List<T> children = getChildrenByName(node, childrenFieldName);
            if (CollectionUtils.isNotEmpty(children)) {
                result.addAll(flattenTree(children, childrenFieldName, clearChildren));
                // 清空子节点（如果需要）
                if (clearChildren) {
                    setChildrenByName(node, new ArrayList<>(), childrenFieldName);
                }
            }
        }

        return result;
    }

    /**
     * 扁平化树形结构（使用默认子节点字段名"children"）
     *
     * @param treeList      树形结构列表
     * @param clearChildren 是否清空子节点
     * @param <T>           实体类型
     * @return 扁平化后的列表
     */
    public static <T> List<T> flattenTree(List<T> treeList, boolean clearChildren) {
        return flattenTree(treeList, DEFAULT_CHILDREN_FIELD, clearChildren);
    }

    /**
     * 扁平化树形结构（使用自定义节点字段名）
     *
     * @param treeList          树形结构列表
     * @param clearChildren     是否清空子节点
     * @param childrenFieldName 子节点字段名
     * @param <T>               实体类型
     * @return 扁平化后的列表
     */
    public static <T> List<T> flattenTree(List<T> treeList, boolean clearChildren, String childrenFieldName) {
        return flattenTree(treeList, childrenFieldName, clearChildren);
    }
}