package com.xpaas.zlkgl.strategy;

import java.util.List;

/**
 * 文件夹操作策略接口
 * 定义不同类型文件夹的通用操作方法
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface FolderOperationStrategy {

    /**
     * 根据ID获取文件夹信息
     *
     * @param folderId 文件夹ID
     * @return 文件夹信息对象
     */
    Object getFolderById(String folderId);

    /**
     * 保存文件夹
     *
     * @param folder 文件夹对象
     * @return 保存后的文件夹对象
     */
    Object saveFolder(Object folder);

    /**
     * 获取子文件夹列表
     *
     * @param parentFolderId 父文件夹ID
     * @return 子文件夹列表
     */
    List<Object> getSubFolders(String parentFolderId);

    /**
     * 获取子文件夹列表（支持排除指定文件夹）
     *
     * @param parentFolderId  父文件夹ID
     * @param excludeFolderId 要排除的文件夹ID
     * @return 子文件夹列表
     */
    List<Object> getSubFolders(String parentFolderId, String excludeFolderId);

    /**
     * 检查文件夹名称是否重复
     *
     * @param folderName      文件夹名称
     * @param parentFolderId  父文件夹ID
     * @param excludeFolderId 排除的文件夹ID（用于更新时排除自己）
     * @return 是否重复
     */
    boolean isFolderNameExists(String folderName, String parentFolderId, String excludeFolderId);

    /**
     * 生成不重复的文件夹名称（用于根级复制）
     *
     * @param originalName   原始名称
     * @param parentFolderId 父文件夹ID
     * @return 不重复的名称
     */
    String generateUniqueFolderName(String originalName, String parentFolderId);

    /**
     * 生成不重复的文件夹名称（支持指定jdId）
     *
     * @param originalName   原始名称
     * @param parentFolderId 父文件夹ID
     * @param sourceFolder   源文件夹对象（用于获取jdId等信息）
     * @return 不重复的名称
     */
    String generateUniqueFolderName(String originalName, String parentFolderId, Object sourceFolder);

    /**
     * 创建文件夹副本
     *
     * @param originalFolder 原始文件夹
     * @param newName        新名称
     * @param targetParentId 目标父文件夹ID
     * @return 新文件夹对象
     */
    Object createFolderCopy(Object originalFolder, String newName, String targetParentId);

    /**
     * 获取文件夹ID
     *
     * @param folder 文件夹对象
     * @return 文件夹ID
     */
    String getFolderId(Object folder);

    /**
     * 获取文件夹名称
     *
     * @param folder 文件夹对象
     * @return 文件夹名称
     */
    String getFolderName(Object folder);

    /**
     * 获取文件夹类型
     *
     * @param folder 文件夹对象
     * @return 文件夹类型（0：外链文件夹，1：普通文件夹）
     */
    String getFolderType(Object folder);
}
