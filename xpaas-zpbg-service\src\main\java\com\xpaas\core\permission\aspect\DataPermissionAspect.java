package com.xpaas.core.permission.aspect;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.permission.annotation.DataPermission;
import com.xpaas.core.permission.checker.DataPermissionChecker;
import com.xpaas.core.permission.enums.OperationType;
import com.xpaas.core.tenant.mp.TenantEntity;
import com.xpaas.core.utils.ReflectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Collections;
import java.util.List;

/**
 * 数据权限AOP切面
 * <p>
 * 实现简单的权限控制：
 * 1. 创建者可以删除/修改/查看自己的数据
 * 2. 管理员可以删除/修改/查看所有数据
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
@Order(1)
public class DataPermissionAspect {

    private final DataPermissionChecker dataPermissionChecker;

    /**
     * 从方法参数中提取ID列表（智能识别）
     */
    private static List<Long> extractIds(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (ObjectUtil.isEmpty(args)) {
                return Collections.emptyList();
            }

            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Parameter[] parameters = method.getParameters();

            // 策略1：查找名为 "ids" 或 "id" 的参数
            for (int i = 0; i < parameters.length; i++) {
                String paramName = parameters[i].getName();
                if ("ids".equals(paramName) || "id".equals(paramName)) {
                    Object paramValue = args[i];
                    if (paramValue != null) {
                        List<Long> ids = ReflectionUtils.convertToIdList(paramValue);
                        if (!ids.isEmpty()) {
                            return ids;
                        }
                    }
                }
            }

            // 策略2：从第一个参数对象中提取 id 字段
            Object firstParam = args[0];
            if (firstParam != null) {
                List<Long> ids = extractIdFromObject(firstParam);
                if (!ids.isEmpty()) {
                    return ids;
                }
            }

            return Collections.emptyList();

        } catch (Exception e) {
            log.error("提取ID列表异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 从对象中提取ID字段
     */
    private static List<Long> extractIdFromObject(Object obj) {
        Object fieldValue = ReflectionUtils.getFieldValue(obj, "id");
        if (fieldValue != null) {
            return ReflectionUtils.convertToIdList(fieldValue);
        }
        return Collections.emptyList();
    }

    @Before("@annotation(dataPermission)")
    public void checkDataPermission(JoinPoint joinPoint, DataPermission dataPermission) {
        try {

            if (dataPermission.operation() == OperationType.VIEW) {
                // 查看操作：修改查询条件
                if (!modifyQueryWrapperInArgs(joinPoint)) {
                    log.warn("未找到QueryWrapper参数，无法应用数据范围过滤，方法: {}",
                            joinPoint.getSignature().getName());
                }
                return;
            }

            // 删除/修改操作：检查具体权限
            handleModifyOperation(joinPoint, dataPermission);

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("数据权限检查异常", e);
            throw new ServiceException("权限检查失败");
        }
    }

    /**
     * 处理删除/修改操作：检查具体权限
     */
    private void handleModifyOperation(JoinPoint joinPoint, DataPermission dataPermission) {
        // 1. 提取ID列表
        List<Long> ids = extractIds(joinPoint);
        if (ObjectUtil.isEmpty(ids)) {
            return;
        }

        // 2. 执行权限检查
        boolean hasPermission = dataPermissionChecker.checkPermission(
                dataPermission.entityClass(),
                ids,
                dataPermission.operation().getDescription()
        );

        // 3. 权限检查失败则抛出异常
        if (!hasPermission) {
            throw new ServiceException(dataPermission.message());
        }
    }

    /**
     * 修改方法参数中的QueryWrapper，应用数据范围过滤
     */
    private boolean modifyQueryWrapperInArgs(JoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (ObjectUtil.isEmpty(args)) {
                return false;
            }

            // 查找参数中的QueryWrapper或LambdaQueryWrapper
            for (Object arg : args) {
                if (arg instanceof Wrapper) {
                    @SuppressWarnings("unchecked") Wrapper<TenantEntity> wrapper = (Wrapper<TenantEntity>) arg;
                    dataPermissionChecker.applyDataScope(wrapper);
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("修改QueryWrapper参数异常", e);
            return false;
        }
    }
}
