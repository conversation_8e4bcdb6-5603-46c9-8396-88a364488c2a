package com.xpaas.zlkgl.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.strategy.FolderOperationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类管理文件夹操作策略实现
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FlglWjjOperationStrategy implements FolderOperationStrategy {

    private static final String COPY_SUFFIX = "_副本";
    private final IFlglWjjService flglWjjService;

    /**
     * 根据ID获取文件夹信息
     */
    @Override
    public Object getFolderById(String folderId) {
        return flglWjjService.getById(folderId);
    }

    /**
     * 保存文件夹
     */
    @Override
    public Object saveFolder(Object folder) {
        FlglWjj flglWjj = (FlglWjj) folder;
        flglWjjService.save(flglWjj);
        return flglWjj;
    }

    /**
     * 获取子文件夹列表
     */
    @Override
    public List<Object> getSubFolders(String parentFolderId) {
        return getSubFolders(parentFolderId, null);
    }

    /**
     * 获取子文件夹列表（支持排除指定文件夹）
     */
    @Override
    public List<Object> getSubFolders(String parentFolderId, String excludeFolderId) {
        LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();

        // 处理父级文件夹ID，空值表示根级别
        if (StringUtil.isBlank(parentFolderId)) {
            // 根级别：父级文件夹ID为null或空字符串
            queryWrapper.and(wrapper -> wrapper.isNull(FlglWjj::getFjWjjId).or().eq(FlglWjj::getFjWjjId, ""));
        } else {
            queryWrapper.eq(FlglWjj::getFjWjjId, parentFolderId);
        }

        // 排除指定的文件夹ID（防止死循环）
        if (StringUtil.isNotBlank(excludeFolderId)) {
            queryWrapper.ne(FlglWjj::getId, excludeFolderId);
        }

        List<FlglWjj> subFolders = flglWjjService.list(queryWrapper);
        return subFolders.stream().map(Object.class::cast).collect(Collectors.toList());
    }

    /**
     * 检查文件夹名称是否重复
     * FlglWjj需要考虑jdId字段，同一个节点下的文件夹名称不能重复
     * 注意：这个方法只用于旧的2参数generateUniqueFolderName，新的3参数方法使用isFolderNameExistsWithJdId
     */
    @Override
    public boolean isFolderNameExists(String folderName, String parentFolderId, String excludeFolderId) {
        // 这个方法主要用于向后兼容，实际使用中应该优先使用带jdId的方法
        String jdId = getJdIdByParentFolderId(parentFolderId);

        LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlglWjj::getWjjMc, folderName);

        // 处理父级文件夹ID，空值表示根级别
        if (StringUtil.isBlank(parentFolderId)) {
            // 根级别：父级文件夹ID为null或空字符串
            queryWrapper.and(wrapper -> wrapper.isNull(FlglWjj::getFjWjjId).or().eq(FlglWjj::getFjWjjId, ""));
        } else {
            queryWrapper.eq(FlglWjj::getFjWjjId, parentFolderId);
        }

        // FlglWjj的jdId不会为空，所以这里应该总是添加jdId条件
        if (StringUtil.isNotBlank(jdId)) {
            queryWrapper.eq(FlglWjj::getJdId, jdId);
        }

        if (StringUtil.isNotBlank(excludeFolderId)) {
            queryWrapper.ne(FlglWjj::getId, excludeFolderId);
        }

        return flglWjjService.count(queryWrapper) > 0;
    }

    /**
     * 生成不重复的文件夹名称
     */
    @Override
    public String generateUniqueFolderName(String originalName, String parentFolderId) {
        String newName = originalName + COPY_SUFFIX;
        int counter = 1;

        // 先检查基础的"_副本"名称是否存在
        while (isFolderNameExists(newName, parentFolderId, null)) {
            counter++;
            newName = originalName + COPY_SUFFIX + counter;
        }

        return newName;
    }

    /**
     * 生成不重复的文件夹名称（支持指定jdId）
     * 这是推荐使用的方法，因为它能正确处理jdId
     */
    @Override
    public String generateUniqueFolderName(String originalName, String parentFolderId, Object sourceFolder) {
        FlglWjj source = (FlglWjj) sourceFolder;
        String jdId = source.getJdId();

        String newName = originalName + COPY_SUFFIX;
        int counter = 1;

        // 使用源文件夹的jdId进行重名检测，确保在同一节点内不重名
        while (isFolderNameExistsWithJdId(newName, parentFolderId, jdId, null)) {
            counter++;
            newName = originalName + COPY_SUFFIX + counter;
        }
        if (StrUtil.length(newName) > 100) {
            throw new ServiceException("名称过长，无法复制");
        }
        return newName;
    }

    /**
     * 创建文件夹副本
     */
    @Override
    public Object createFolderCopy(Object originalFolder, String newName, String targetParentId) {
        FlglWjj original = (FlglWjj) originalFolder;
        FlglWjj copy = new FlglWjj();

        // 复制属性
        BeanUtils.copyProperties(original, copy);

        // 设置新的属性
        copy.setId(null);
        copy.setWjjMc(newName);
        copy.setFjWjjId(targetParentId);
        copy.setZhid(null);

        return copy;
    }

    /**
     * 获取文件夹ID
     */
    @Override
    public String getFolderId(Object folder) {
        FlglWjj flglWjj = (FlglWjj) folder;
        return String.valueOf(flglWjj.getId());
    }

    /**
     * 获取文件夹名称
     */
    @Override
    public String getFolderName(Object folder) {
        FlglWjj flglWjj = (FlglWjj) folder;
        return flglWjj.getWjjMc();
    }

    /**
     * 获取文件夹类型
     */
    @Override
    public String getFolderType(Object folder) {
        FlglWjj flglWjj = (FlglWjj) folder;
        return flglWjj.getWjjLx();
    }

    /**
     * 检查文件夹名称是否重复（指定jdId）
     * 这是最准确的重名检测方法，直接使用传入的jdId
     *
     * @param folderName      文件夹名称
     * @param parentFolderId  父级文件夹ID
     * @param jdId            节点ID（FlglWjj中不会为空）
     * @param excludeFolderId 排除的文件夹ID
     * @return 是否重复
     */
    private boolean isFolderNameExistsWithJdId(String folderName, String parentFolderId, String jdId, String excludeFolderId) {
        LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlglWjj::getWjjMc, folderName);

        // FlglWjj的jdId不会为空，直接使用传入的jdId
        queryWrapper.eq(FlglWjj::getJdId, jdId);

        // 处理父级文件夹ID，空值表示根级别
        if (StringUtil.isBlank(parentFolderId)) {
            // 根级别：父级文件夹ID为null或空字符串
            queryWrapper.and(wrapper -> wrapper.isNull(FlglWjj::getFjWjjId).or().eq(FlglWjj::getFjWjjId, ""));
        } else {
            queryWrapper.eq(FlglWjj::getFjWjjId, parentFolderId);
        }

        if (StringUtil.isNotBlank(excludeFolderId)) {
            queryWrapper.ne(FlglWjj::getId, excludeFolderId);
        }

        return flglWjjService.count(queryWrapper) > 0;
    }

    /**
     * 根据父级文件夹ID获取jdId
     * 如果是根级别，需要从源文件夹获取jdId
     *
     * @param parentFolderId 父级文件夹ID
     * @return jdId
     */
    private String getJdIdByParentFolderId(String parentFolderId) {
        if (StringUtil.isBlank(parentFolderId)) {
            // 根级别，需要从其他地方获取jdId，这里先返回null
            // 实际使用时需要传入源文件夹的jdId
            return null;
        }

        FlglWjj parentFolder = flglWjjService.getById(parentFolderId);
        if (parentFolder != null) {
            return parentFolder.getJdId();
        }

        return null;
    }
}
