package com.xpaas.core.permission.enums;

import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import lombok.Getter;

/**
 * 文件夹类型枚举
 *
 * <AUTHOR>
 * @since 2025/08/06
 **/
@Getter
public enum FolderType {
    FLGL_WJJ(FlglWjj.class, "分类管理文件夹"),
    WXZL_WJJ(WxzlWjj.class, "外校资料文件夹");

    private final Class<?> entityClass;
    private final String description;

    FolderType(Class<?> entityClass, String description) {
        this.entityClass = entityClass;
        this.description = description;
    }

    /**
     * 根据实体类获取文件夹类型
     */
    public static FolderType getByClass(Class<?> clazz) {
        for (FolderType type : values()) {
            if (type.entityClass.equals(clazz)) {
                return type;
            }
        }
        throw new IllegalArgumentException("不支持的文件夹类型: " + clazz.getSimpleName());
    }
}
