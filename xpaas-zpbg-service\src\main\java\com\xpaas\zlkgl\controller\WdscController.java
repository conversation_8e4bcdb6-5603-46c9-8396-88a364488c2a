package com.xpaas.zlkgl.controller;

import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.service.IZlglService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import com.xpaas.zlkgl.wrapper.WdscWrapper;
import com.xpaas.zlkgl.service.IWdscService;
import com.xpaas.core.boot.ctrl.BaseController;
/**
 * 教学评价-资料库平台-我的收藏表 控制器
 * controller 入口
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wdsc")
@Api(value = "教学评价-资料库平台-我的收藏表", tags = "教学评价-资料库平台-我的收藏表接口")
public class WdscController extends BaseController {
	private WdscWrapper wdscWrapper;
	private IWdscService wdscService;
	private IZlglService zlglService;

	/**
	 * 根据主键集合查询 教学评价-资料库平台-我的收藏表 数据
	 * @param ids 主键集合
	 * <AUTHOR>  作者
	 * @since 2025-08-04 日期
	 */
	@GetMapping("/listByIds")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据IDs查询", notes = "传入ids")
	public R<List<WdscVO>> listByIds(String ids) {
		List<Wdsc> listByIds = wdscService.listByIds(Func.toLongList(ids));
		return R.data(wdscWrapper.listVO(listByIds));
	}

	/**
	 * 高级查询，界面字段的高级搜索
	 * (开发过程中根据需求调整精确查询或模糊查询)
	 * @param map 高级查询字段,请参考高级查询逻辑
	 * @param query 查询条件
	 * <AUTHOR> 作者
	 * @since 2025-08-04 日期
	 */
	@GetMapping("/search")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "高级查询", notes = "传入字段_条件")
	public R<IPage<WdscVO>> search(@RequestParam Map<String, Object> map, Query query){
		QueryWrapper<Wdsc> queryWrapper = Condition.getQueryWrapper(map, Wdsc.class);
		IPage<Wdsc> pages = wdscService.page(Condition.getPage(query), queryWrapper);
		return R.data(wdscWrapper.pageVO(pages));
	}

	/**
	 * 新增或修改 教学评价-资料库平台-我的收藏表 (优先使用save或update接口)
	 * id存在的情况下进行更新操作，id不存在进行插入操作
	 * @param wdsc wdsc实体
	 * <AUTHOR> 作者
	 * @since 2025-08-04 日期
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入wdsc")
	public R submit(@Valid @RequestBody Wdsc wdsc) {
		//我的收藏表操作校验,校验出错时返回报错提示
		String erro = checkWdscVO(wdsc);
		if (StringUtil.isNotBlank(erro)) {
			return R.fail(erro);
		}
		return R.status(wdscService.saveOrUpdate(wdsc));
	}

	/**
	 * 抽离我的收藏 新增、修改公共校验部分
	 * @param wdsc
	 * @return
	 */
	String checkWdscVO(Wdsc wdsc){
		if(Func.isEmpty(wdsc) || StringUtil.isEmpty(wdsc.getCjr())){
			return "获取当前登陆人信息失败，请刷新后重试";
		}
		if(Func.isEmpty(wdsc) || StringUtil.isEmpty(wdsc.getSjId())){
			return "无法获取该文件id";
		}
		//校验该资料是否有效
		QueryWrapper<Zlgl> qwZl = new QueryWrapper<>();
		qwZl.eq("id", wdsc.getSjId());
		int rowsById = zlglService.count(qwZl);
		if(rowsById==0){
			return "该文件已被删除，无法收藏";
		}
		//todo:校验是否有取消收藏的记录，将记录恢复，避免数据冗余
		return null;
	}

	
	/**
	 * 删除 教学评价-资料库平台-我的收藏表
	 * 根据主键ID集合逻辑删除数据
	 * @param ids 主键集合
	 * <AUTHOR> 作者
	 * @since 2025-08-04 集合
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		boolean b = wdscService.deleteLogic(Func.toLongList(ids));
		return R.status(b);
	}

	
	/**
	 * 导出Excel
	 * @param response 返回响应
	 * @param fileName 文件名
	 * @param sheetName sheet页名称
	 * @param columnNames 要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段
	 * @param ids 要导出的id,多个id用逗号连接.如果为空,将导出全部数据
	 * @param ascs 正排序字段,多个字段用逗号连接
	 * @param descs 倒排序字段,多个字段用逗号连接
	 * @param map 高级查询字段,请参考高级查询逻辑
	 * <AUTHOR> 作者
	 * @since 2025-08-04 日期
	 */
	@GetMapping("/export")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "导出Excel", notes = "导出Excel")
	public void exportExcel(HttpServletResponse response,
							@ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
							@ApiParam(value = "sheet页名称") String sheetName,
							@ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
							@ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
							@ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
							@ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
							@ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
		//剔除非实体类字段
		map.remove("fileName");
		map.remove("sheetName");
		map.remove("columnNames");
		map.remove("ids");
		map.remove("ascs");
		map.remove("descs");
		QueryWrapper<Wdsc> queryWrapper = Condition.getQueryWrapper(map, Wdsc.class);
		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		//指定id
		if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0){
			queryWrapper.in("id", Arrays.asList(ids.split(",")));
		}
		//正排序
		if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(ascs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByAsc(tmpList);
		}
		//倒排序
		if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0){
			String[] tmpList = Func.toStrArray(descs);
			for (int i = 0; i < tmpList.length; i++){
				tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
			}
			queryWrapper.orderByDesc(tmpList);
		}
		//设置sheetName
		if (StringUtil.isBlank(sheetName)){
			sheetName = fileName;
		}
		List<Wdsc> list = wdscService.list(queryWrapper);
		ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Wdsc.class);
	}


	/**
	 * 导入Excel
	 * @param file 文件名
	 * <AUTHOR> 作者
	 * @since 2025-08-04 日期
	 */
	@PostMapping("/import")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "导入Excel", notes = "导入Excel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		List<Wdsc> list = ExcelUtil.read(file, Wdsc.class);
		//TODO 此处需要根据具体业务添加代码
		wdscService.saveBatch(list);
		return R.status(true);
	}

	/**
	 * 下载导入模板
	 * @param response 返回的响应数据
	 * @param columnNames 导入模板的字段
	 * <AUTHOR> 作者
	 * @since 2025-08-04 创建日期
	 */
	@GetMapping("/template")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "下载导入模板", notes = "下载导入模板")
	public void template(HttpServletResponse response,
                         @ApiParam(value = "要导出的字段,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames) {
		QueryWrapper<Wdsc> queryWrapper = new QueryWrapper<>();
		queryWrapper.last("limit 1");
		List<Wdsc> list = wdscService.list(queryWrapper);
		//TODO 此处需要根据具体业务添加代码

		//要导出的字段列表
		List<String> columnFiledNames = new ArrayList<>();
		if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0){
			columnFiledNames = Arrays.asList(columnNames.split(","));
		}
		ExcelUtil.export(response, "Wdsc导入模板", "Wdsc导入模板",columnFiledNames, list, Wdsc.class);
	}
}
