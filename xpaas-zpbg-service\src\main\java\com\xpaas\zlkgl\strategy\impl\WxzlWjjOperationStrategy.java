package com.xpaas.zlkgl.strategy.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.strategy.FolderOperationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 外校资料文件夹操作策略实现
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WxzlWjjOperationStrategy implements FolderOperationStrategy {

    private static final String COPY_SUFFIX = "_副本";
    private final IWxzlWjjService wxzlWjjService;

    /**
     * 根据ID获取文件夹信息
     */
    @Override
    public Object getFolderById(String folderId) {
        return wxzlWjjService.getById(folderId);
    }

    /**
     * 保存文件夹
     */
    @Override
    public Object saveFolder(Object folder) {
        WxzlWjj wxzlWjj = (WxzlWjj) folder;
        wxzlWjjService.save(wxzlWjj);
        return wxzlWjj;
    }

    /**
     * 获取子文件夹列表
     */
    @Override
    public List<Object> getSubFolders(String parentFolderId) {
        return getSubFolders(parentFolderId, null);
    }

    /**
     * 获取子文件夹列表（支持排除指定文件夹）
     */
    @Override
    public List<Object> getSubFolders(String parentFolderId, String excludeFolderId) {
        LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();

        // 处理父级文件夹ID，空值表示根级别
        if (StringUtil.isBlank(parentFolderId)) {
            queryWrapper.and(wrapper -> wrapper.isNull(WxzlWjj::getFjWjjId).or().eq(WxzlWjj::getFjWjjId, ""));
        } else {
            queryWrapper.eq(WxzlWjj::getFjWjjId, parentFolderId);
        }

        // 排除指定的文件夹ID（防止死循环）
        if (StringUtil.isNotBlank(excludeFolderId)) {
            queryWrapper.ne(WxzlWjj::getId, excludeFolderId);
        }

        List<WxzlWjj> subFolders = wxzlWjjService.list(queryWrapper);
        return subFolders.stream().map(Object.class::cast).collect(Collectors.toList());
    }

    /**
     * 检查文件夹名称是否重复
     */
    @Override
    public boolean isFolderNameExists(String folderName, String parentFolderId, String excludeFolderId) {
        LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxzlWjj::getWjjMc, folderName);

        // 处理父级文件夹ID，空值表示根级别
        if (StringUtil.isBlank(parentFolderId)) {
            queryWrapper.and(wrapper -> wrapper.isNull(WxzlWjj::getFjWjjId).or().eq(WxzlWjj::getFjWjjId, ""));
        } else {
            queryWrapper.eq(WxzlWjj::getFjWjjId, parentFolderId);
        }

        if (StringUtil.isNotBlank(excludeFolderId)) {
            queryWrapper.ne(WxzlWjj::getId, excludeFolderId);
        }

        return wxzlWjjService.count(queryWrapper) > 0;
    }

    /**
     * 生成不重复的文件夹名称
     */
    @Override
    public String generateUniqueFolderName(String originalName, String parentFolderId) {
        String newName = originalName + COPY_SUFFIX;
        int counter = 1;

        // 先检查基础的"_副本"名称是否存在
        while (isFolderNameExists(newName, parentFolderId, null)) {
            counter++;
            newName = originalName + COPY_SUFFIX + counter;
        }
        if (StrUtil.length(newName) > 100) {
            throw new ServiceException("名称过长，无法复制");
        }
        return newName;
    }

    /**
     * 生成不重复的文件夹名称（支持指定jdId）
     * WxzlWjj不需要考虑jdId，所以直接调用原方法
     */
    @Override
    public String generateUniqueFolderName(String originalName, String parentFolderId, Object sourceFolder) {
        // WxzlWjj不需要考虑jdId，直接调用原方法
        return generateUniqueFolderName(originalName, parentFolderId);
    }

    /**
     * 创建文件夹副本
     */
    @Override
    public Object createFolderCopy(Object originalFolder, String newName, String targetParentId) {
        WxzlWjj original = (WxzlWjj) originalFolder;
        WxzlWjj copy = new WxzlWjj();

        // 复制属性
        BeanUtils.copyProperties(original, copy);

        // 设置新的属性
        copy.setId(null);
        copy.setWjjMc(newName);
        copy.setFjWjjId(targetParentId);
        copy.setZhid(null);

        return copy;
    }

    /**
     * 获取文件夹ID
     */
    @Override
    public String getFolderId(Object folder) {
        WxzlWjj wxzlWjj = (WxzlWjj) folder;
        return String.valueOf(wxzlWjj.getId());
    }

    /**
     * 获取文件夹名称
     */
    @Override
    public String getFolderName(Object folder) {
        WxzlWjj wxzlWjj = (WxzlWjj) folder;
        return wxzlWjj.getWjjMc();
    }

    /**
     * 获取文件夹类型
     */
    @Override
    public String getFolderType(Object folder) {
        WxzlWjj wxzlWjj = (WxzlWjj) folder;
        return wxzlWjj.getWjjLx();
    }
}
