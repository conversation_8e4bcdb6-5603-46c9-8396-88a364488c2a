package com.xpaas.zlkgl.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 教学评价-资料库平台-资料管理表实体类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@TableName("T_TD_JXPJ_ZLK_ZLGL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Zlgl对象", description = "教学评价-资料库平台-资料管理表")
public class Zlgl extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 文件夹树id
	*/
	@ExcelProperty("文件夹树id")
	@ApiModelProperty(value = "文件夹树id")
	private String wjjId;

	/**
	* 资料名称
	*/
	@ExcelProperty("资料名称")
	@ApiModelProperty(value = "资料名称")
	private String zlMc;

	/**
	* 资料重命名
	*/
	@ExcelProperty("资料重命名")
	@ApiModelProperty(value = "资料重命名")
	private String zlCmm;

	/**
	* 排序
	*/
	@ExcelProperty("排序")
	@ApiModelProperty(value = "排序")
	private Integer zlPx;

	/**
	* 资料地址
	*/
	@ExcelProperty("资料地址")
	@ApiModelProperty(value = "资料地址")
	private String zlDz;

	/**
	* 密级
	*/
	@ExcelProperty("密级")
	@ApiModelProperty(value = "密级")
	private String zlMj;

	/**
	* 是否为外校资料类型（1：是 0：否）
	*/
	@ExcelProperty("是否为外校资料类型（1：是 0：否）")
	@ApiModelProperty(value = "是否为外校资料类型（1：是 0：否）")
	private String wxzlLx;

	/**
	* 评价类型
	*/
	@ExcelProperty("评价类型")
	@ApiModelProperty(value = "评价类型")
	private String pjLx;

	/**
	 * 文件格式
	 */
	@ExcelProperty("文件格式")
	@ApiModelProperty(value = "文件格式")
	private String wjGs;

	/**
	 * 文件大小
	 */
	@ExcelProperty("文件大小")
	@ApiModelProperty(value = "文件大小")
	private String wjDx;



}
