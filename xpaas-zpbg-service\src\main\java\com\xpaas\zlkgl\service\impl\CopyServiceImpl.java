package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.enums.FolderTypeEnum;
import com.xpaas.zlkgl.model.FolderSnapshot;
import com.xpaas.zlkgl.service.ICopyService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.strategy.FolderOperationStrategy;
import com.xpaas.zlkgl.strategy.impl.FlglWjjOperationStrategy;
import com.xpaas.zlkgl.strategy.impl.WxzlWjjOperationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 复制服务实现类
 * 提供统一的文件和文件夹复制功能
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CopyServiceImpl implements ICopyService {

    private static final String COPY_SUFFIX = "_副本";
    private static final String EXTERNAL_LINK_FOLDER_TYPE = "0";
    private final IZlglService zlglService;
    private final FlglWjjOperationStrategy flglWjjStrategy;
    private final WxzlWjjOperationStrategy wxzlWjjStrategy;

    /**
     * 获取文件夹操作策略
     *
     * @param folderType 文件夹类型
     * @return 对应的策略实现
     */
    private FolderOperationStrategy getFolderStrategy(FolderTypeEnum folderType) {
        switch (folderType) {
            case FLGL:
                return flglWjjStrategy;
            case WXZL:
                return wxzlWjjStrategy;
            default:
                throw new IllegalArgumentException("不支持的文件夹类型: " + folderType);
        }
    }

    /**
     * 复制文件（根级复制，需要重名处理）
     * 文件表是共用的，所以不需要区分文件夹类型
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyFile(String fileId, String targetFolderId) {
        return copyFile(fileId, targetFolderId, true);
    }

    /**
     * 复制文件（保持原名，用于子文件复制）
     *
     * @param fileId         源文件ID
     * @param targetFolderId 目标文件夹ID
     * @return 复制是否成功
     */
    private boolean copyFileWithOriginalName(String fileId, String targetFolderId) {
        return copyFile(fileId, targetFolderId, false);
    }

    /**
     * 复制文件（内部方法，支持控制是否需要重名处理）
     *
     * @param fileId         源文件ID
     * @param targetFolderId 目标文件夹ID
     * @param needRename     是否需要重名处理
     * @return 复制是否成功
     */
    private boolean copyFile(String fileId, String targetFolderId, boolean needRename) {
        // 检查目标文件夹是否为外链文件夹
        checkTargetFolderType(targetFolderId);

        // 获取源文件信息
        Zlgl sourceFile = zlglService.getById(fileId);
        if (sourceFile == null) {
            return false;
        }

        // 创建文件副本
        Zlgl copyFile = new Zlgl();
        BeanUtils.copyProperties(sourceFile, copyFile);

        // 设置新的属性
        copyFile.setId(null);
        copyFile.setWjjId(targetFolderId);
        copyFile.setZhid(null);

        // 设置文件名
        String newFileName;
        if (needRename) {
            // 根级复制：需要重名处理，添加"_副本"后缀
            newFileName = generateUniqueFileName(sourceFile.getZlMc(), targetFolderId);
        } else {
            // 子级复制：保持原名
            newFileName = sourceFile.getZlMc();
        }
        copyFile.setZlMc(newFileName);

        // 保存文件副本
        return zlglService.save(copyFile);


    }

    /**
     * 复制文件夹
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyFolder(String folderId, String targetFolderId, FolderTypeEnum folderType) {
        return copyFolder(folderId, targetFolderId, folderType, true);
    }

    /**
     * 复制文件夹（内部方法，支持控制是否为根级复制）
     *
     * @param folderId       源文件夹ID
     * @param targetFolderId 目标文件夹ID
     * @param folderType     文件夹类型
     * @param isRootCopy     是否为根级复制（只有根级复制才需要重名处理）
     * @return 复制是否成功
     */
    private boolean copyFolder(String folderId, String targetFolderId, FolderTypeEnum folderType, boolean isRootCopy) {
        // 检查目标文件夹是否为外链文件夹
        checkTargetFolderType(targetFolderId);

        FolderOperationStrategy strategy = getFolderStrategy(folderType);

        // 获取源文件夹信息
        Object sourceFolder = strategy.getFolderById(folderId);
        if (sourceFolder == null) {
            return false;
        }

        // 如果是根级复制，先构建完整的文件夹结构快照（避免时序问题）
        FolderSnapshot snapshot = null;
        if (isRootCopy) {
            snapshot = buildFolderSnapshot(folderId, strategy);
        }

        // 生成文件夹名称
        String originalName = strategy.getFolderName(sourceFolder);
        String newFolderName;

        if (isRootCopy) {
            // 根级复制：需要重名处理，添加"_副本"后缀
            // 使用新方法，支持jdId处理
            newFolderName = strategy.generateUniqueFolderName(originalName, targetFolderId, sourceFolder);
        } else {
            // 子级复制：保持原名
            newFolderName = originalName;
        }

        // 创建文件夹副本
        Object copyFolder = strategy.createFolderCopy(sourceFolder, newFolderName, targetFolderId);
        Object savedFolder = strategy.saveFolder(copyFolder);
        String newFolderId = strategy.getFolderId(savedFolder);

        // 检查文件夹类型，如果是外链文件夹（wjjLx=0），则不复制子内容
        String folderTypeValue = strategy.getFolderType(sourceFolder);
        if (EXTERNAL_LINK_FOLDER_TYPE.equals(folderTypeValue)) {
            return true;
        }

        // 递归复制子文件夹和文件
        if (isRootCopy) {
            // 根级复制：使用快照避免死循环
            return copyFolderContentsFromSnapshot(snapshot, folderId, newFolderId, folderType);
        } else {
            // 子级复制：使用排除机制防止死循环
            return copyFolderContentsWithExclusion(folderId, newFolderId, folderType, newFolderId);
        }
    }



    /**
     * 根据文件夹ID获取文件列表
     *
     * @param folderId 文件夹ID
     * @return 文件列表
     */
    private List<Zlgl> getFilesByFolderId(String folderId) {
        LambdaQueryWrapper<Zlgl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Zlgl::getWjjId, folderId);
        return zlglService.list(queryWrapper);
    }

    /**
     * 生成不重复的文件名
     *
     * @param originalName 原始文件名
     * @param folderId     文件夹ID
     * @return 不重复的文件名
     */
    private String generateUniqueFileName(String originalName, String folderId) {
        // 处理文件扩展名
        String nameWithoutExt = originalName;
        String extension = "";

        int lastDotIndex = originalName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            nameWithoutExt = originalName.substring(0, lastDotIndex);
            extension = originalName.substring(lastDotIndex);
        }

        String newName = nameWithoutExt + COPY_SUFFIX + extension;
        int counter = 1;

        // 先检查基础的"_副本"名称是否存在
        while (isFileNameExists(newName, folderId)) {
            counter++;
            newName = nameWithoutExt + COPY_SUFFIX + counter + extension;
        }

        return newName;
    }

    /**
     * 检查文件名是否存在
     *
     * @param fileName 文件名
     * @param folderId 文件夹ID
     * @return 是否存在
     */
    private boolean isFileNameExists(String fileName, String folderId) {
        LambdaQueryWrapper<Zlgl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Zlgl::getZlMc, fileName)
                .eq(Zlgl::getWjjId, folderId);
        return zlglService.count(queryWrapper) > 0;
    }

    /**
     * 检查目标文件夹类型，如果是外链文件夹则抛出异常
     *
     * @param targetFolderId 目标文件夹ID
     * @throws ServiceException 如果目标文件夹是外链文件夹
     */
    private void checkTargetFolderType(String targetFolderId) {
        // 如果目标文件夹ID为空，表示复制到根级别，允许复制
        if (targetFolderId == null || targetFolderId.trim().isEmpty()) {
            return;
        }

        // 检查分类管理文件夹
        Object flglFolder = flglWjjStrategy.getFolderById(targetFolderId);
        if (flglFolder != null) {
            String folderType = flglWjjStrategy.getFolderType(flglFolder);
            if (EXTERNAL_LINK_FOLDER_TYPE.equals(folderType)) {
                throw new ServiceException("不能复制到外链文件夹中");
            }
            return;
        }

        // 检查外校资料文件夹
        Object wxzlFolder = wxzlWjjStrategy.getFolderById(targetFolderId);
        if (wxzlFolder != null) {
            String folderType = wxzlWjjStrategy.getFolderType(wxzlFolder);
            if (EXTERNAL_LINK_FOLDER_TYPE.equals(folderType)) {
                throw new ServiceException("不能复制到外链文件夹中");
            }
            return;
        }

        // 如果两种类型的文件夹都找不到，说明目标文件夹不存在
        throw new ServiceException("目标文件夹不存在");
    }

    /**
     * 复制分类管理文件夹
     */
    @Override
    public boolean copyFlglFolder(String folderId, String targetFolderId) {
        return copyFolder(folderId, targetFolderId, FolderTypeEnum.FLGL);
    }

    /**
     * 复制外校资料文件夹
     */
    @Override
    public boolean copyWxzlFolder(String folderId, String targetFolderId) {
        return copyFolder(folderId, targetFolderId, FolderTypeEnum.WXZL);
    }

    /**
     * 构建文件夹结构快照
     * 在复制开始前获取完整的文件夹树结构，避免复制过程中的时序问题
     *
     * @param rootFolderId 根文件夹ID
     * @param strategy     文件夹操作策略
     * @return 文件夹结构快照
     */
    private FolderSnapshot buildFolderSnapshot(String rootFolderId, FolderOperationStrategy strategy) {
        FolderSnapshot snapshot = new FolderSnapshot();
        snapshot.setRootFolderId(rootFolderId);

        Map<String, List<Object>> subFoldersMap = new HashMap<>();
        Map<String, List<Object>> filesMap = new HashMap<>();

        // 使用广度优先遍历构建完整的文件夹树快照
        Queue<String> folderQueue = new LinkedList<>();
        folderQueue.offer(rootFolderId);

        while (!folderQueue.isEmpty()) {
            String currentFolderId = folderQueue.poll();

            // 获取子文件夹（不排除任何文件夹，获取完整快照）
            List<Object> subFolders = strategy.getSubFolders(currentFolderId);
            subFoldersMap.put(currentFolderId, subFolders);

            // 将子文件夹加入队列继续遍历
            for (Object subFolder : subFolders) {
                String subFolderId = strategy.getFolderId(subFolder);
                folderQueue.offer(subFolderId);
            }

            // 获取文件列表
            List<Zlgl> fileList = getFilesByFolderId(currentFolderId);
            List<Object> files = fileList.stream().map(Object.class::cast).collect(java.util.stream.Collectors.toList());
            filesMap.put(currentFolderId, files);
        }

        snapshot.setSubFoldersMap(subFoldersMap);
        snapshot.setFilesMap(filesMap);

        return snapshot;
    }

    /**
     * 使用快照复制文件夹内容
     *
     * @param snapshot       文件夹结构快照
     * @param sourceFolderId 源文件夹ID
     * @param targetFolderId 目标文件夹ID
     * @param folderType     文件夹类型
     * @return 复制是否成功
     */
    private boolean copyFolderContentsFromSnapshot(FolderSnapshot snapshot, String sourceFolderId, String targetFolderId, FolderTypeEnum folderType) {
        FolderOperationStrategy strategy = getFolderStrategy(folderType);

        // 复制子文件夹（使用快照数据）
        List<Object> subFolders = snapshot.getSubFolders(sourceFolderId);
        for (Object subFolder : subFolders) {
            String subFolderId = strategy.getFolderId(subFolder);
            String subFolderName = strategy.getFolderName(subFolder);

            // 创建子文件夹副本
            Object copySubFolder = strategy.createFolderCopy(subFolder, subFolderName, targetFolderId);
            Object savedSubFolder = strategy.saveFolder(copySubFolder);
            String newSubFolderId = strategy.getFolderId(savedSubFolder);

            // 检查是否为外链文件夹
            String folderTypeValue = strategy.getFolderType(subFolder);
            if (!EXTERNAL_LINK_FOLDER_TYPE.equals(folderTypeValue)) {
                // 递归复制子文件夹内容
                if (!copyFolderContentsFromSnapshot(snapshot, subFolderId, newSubFolderId, folderType)) {
                    return false;
                }
            }
        }

        // 复制文件（使用快照数据）
        List<Object> files = snapshot.getFiles(sourceFolderId);
        for (Object file : files) {
            Zlgl zlgl = (Zlgl) file;
            String fileId = String.valueOf(zlgl.getId());
            if (!copyFileWithOriginalName(fileId, targetFolderId)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 使用排除机制复制文件夹内容
     *
     * @param sourceFolderId  源文件夹ID
     * @param targetFolderId  目标文件夹ID
     * @param folderType      文件夹类型
     * @param excludeFolderId 要排除的文件夹ID
     * @return 复制是否成功
     */
    private boolean copyFolderContentsWithExclusion(String sourceFolderId, String targetFolderId, FolderTypeEnum folderType, String excludeFolderId) {
        FolderOperationStrategy strategy = getFolderStrategy(folderType);

        // 复制子文件夹（排除指定文件夹）
        List<Object> subFolders = strategy.getSubFolders(sourceFolderId, excludeFolderId);
        for (Object subFolder : subFolders) {
            String subFolderId = strategy.getFolderId(subFolder);
            if (!copyFolder(subFolderId, targetFolderId, folderType, false)) {
                return false;
            }
        }

        // 复制文件
        List<Zlgl> fileList = getFilesByFolderId(sourceFolderId);
        for (Zlgl file : fileList) {
            String fileId = String.valueOf(file.getId());
            if (!copyFileWithOriginalName(fileId, targetFolderId)) {
                return false;
            }
        }

        return true;
    }
}
