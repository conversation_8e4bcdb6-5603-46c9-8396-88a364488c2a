package com.xpaas.core.permission.dto;

import com.xpaas.core.permission.enums.FolderType;
import lombok.Data;

/**
 * 文件夹信息DTO
 *
 * <AUTHOR>
 * @since 2025/08/06
 */
@Data
public class FolderInfo {
    
    /**
     * 文件夹类型
     */
    private FolderType folderType;
    
    /**
     * 创建者ID
     */
    private Long creatorId;
    
    /**
     * 是否存在
     */
    private boolean exists;
    
    public FolderInfo(FolderType folderType, Long creatorId) {
        this.folderType = folderType;
        this.creatorId = creatorId;
        this.exists = true;
    }
    
    private FolderInfo() {
        this.exists = false;
    }
    
    /**
     * 创建不存在的文件夹信息
     */
    public static FolderInfo notFound() {
        return new FolderInfo();
    }
}
