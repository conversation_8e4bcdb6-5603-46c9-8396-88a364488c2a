package com.xpaas.zlkgl.vo;

import com.xpaas.zlkgl.entity.Hsz;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
/**
 * 教学评价-资料库平台-回收站表视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "HszVO对象", description = "教学评价-资料库平台-回收站表")
public class HszVO extends Hsz {
	private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "创建人")
    private String cjrName;
    @ApiModelProperty(value = "修改人")
    private String gxrName;

}
