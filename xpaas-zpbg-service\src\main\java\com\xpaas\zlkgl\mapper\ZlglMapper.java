package com.xpaas.zlkgl.mapper;

import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.vo.ZlglVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import org.springframework.stereotype.Repository;

/**
 * 教学评价-资料库平台-资料管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Repository
public interface ZlglMapper extends BaseMapper<Zlgl> {

	/**
	 * 自定义分页
	 * 分页查询教学评价-资料库平台-资料管理表表数据
	 * @param page
	 * @param zlgl
	 * <AUTHOR>
	 * @since 2025-07-25
	 * @return
	 */
	List<ZlglVO> selectZlglPage(IPage page, ZlglVO zlgl);

}
