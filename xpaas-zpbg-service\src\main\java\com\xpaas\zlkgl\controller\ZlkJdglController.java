package com.xpaas.zlkgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.permission.annotation.DataPermission;
import com.xpaas.core.permission.enums.OperationType;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.ZlkJdgl;
import com.xpaas.zlkgl.service.IZlkJdglService;
import com.xpaas.zlkgl.vo.ZlkJdglVO;
import com.xpaas.zlkgl.wrapper.ZlkJdglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * @program: xpaas-zpbg
 * @description: 教学评价-资料库-节点管理 控制器
 * @author: Cheng Rong Yu
 * @create: 2025-07-24 13:38
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zlkjdgl")
@Api(value = "教学评价-资料库平台-节点管理表", tags = "教学评价-资料库平台-节点管理表接口")
public class ZlkJdglController {

    //节点管理构造器
    private ZlkJdglWrapper jdglWrapper;
    //节点管理接口
    private IZlkJdglService jdglService;

    // 默认"显示在时间轴上"的状态值
    private static final Integer SHOW_ON_TIMELINE = 1;

    /**
     * 获取详情数据
     *
     * @param jdgl jdgl实体
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入jdgl")
    public R<ZlkJdglVO> detail(ZlkJdgl jdgl) {
        ZlkJdgl detail = jdglService.getOne(Condition.getQueryWrapper(jdgl));
        return R.data(jdglWrapper.entityVO(detail));
    }

    /**
     * 根据主键集合查询 教学评价-资料库平台-节点管理表 数据
     *
     * @param ids 主键集合
     * <AUTHOR>  作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/listByIds")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据IDs查询", notes = "传入ids")
    public R<List<ZlkJdglVO>> listByIds(String ids) {
        List<ZlkJdgl> listByIds = jdglService.listByIds(Func.toLongList(ids));
        return R.data(jdglWrapper.listVO(listByIds));
    }

    /**
     * 根据条件查询 教学评价-资料库平台-节点管理表 数据
     *
     * @param jdgl jdgl实体
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "列表", notes = "传入jdgl")
    public R<List<ZlkJdglVO>> list(ZlkJdgl jdgl) {
        List<ZlkJdgl> lists = jdglService.list(Condition.getQueryWrapper(jdgl));
        return R.data(jdglWrapper.listVO(lists));
    }

    /**
     * 分页查询 教学评价-资料库平台-节点管理表数据
     *
     * @param jdgl  jdgl实体
     * @param query 查询条件
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入jdgl")
    public R<IPage<ZlkJdglVO>> page(ZlkJdglVO jdgl, Query query) {
        IPage<ZlkJdgl> pages = jdglService.page(Condition.getPage(query), Condition.getQueryWrapper(jdgl));
        return R.data(jdglWrapper.pageVO(pages));
    }

    /**
     * 高级查询，界面字段的高级搜索
     * (开发过程中根据需求调整精确查询或模糊查询)
     *
     * @param map   高级查询字段,请参考高级查询逻辑
     * @param query 查询条件
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入字段_条件")
    public R<IPage<ZlkJdglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        QueryWrapper<ZlkJdgl> queryWrapper = Condition.getQueryWrapper(map, ZlkJdgl.class);
        //按照节点时间的开始时间倒序展示
        queryWrapper.orderByDesc("STR_TO_DATE(SUBSTRING_INDEX(jd_sj, '-', 1), '%Y/%m/%d')");
        IPage<ZlkJdgl> pages = jdglService.page(Condition.getPage(query), queryWrapper);
        return R.data(jdglWrapper.pageVO(pages));
    }


    /**
     * 新增 教学评价-资料库平台-节点管理表
     *
     * @param jdglVO jdglVO实体
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入jdgl")
    public R save(@Valid @RequestBody ZlkJdglVO jdglVO) {
        //节点管理操作校验,校验出错时返回报错提示
        String erro = checkJdglVo(jdglVO);
        if (StringUtil.isNotBlank(erro)) {
            return R.fail(erro);
        }
        //当是否在时间轴显示字段为空时，默认显示在时间轴上
        jdglVO.setSjzXs(Optional.ofNullable(jdglVO.getSjzXs()).orElse(SHOW_ON_TIMELINE));
        boolean b = jdglService.save(jdglVO);
        return R.status(b);
    }

    /**
     * 修改 教学评价-资料库平台-节点管理表
     * 根据主键ID修改数据
     *
     * @param jdglVO jdglVO实体
     * <AUTHOR>
     * @since 2025-07-24
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入jdgl")
    @DataPermission(entityClass = ZlkJdgl.class, operation = OperationType.UPDATE, message = "无权限修改该节点信息")
    public R update(@Valid @RequestBody ZlkJdglVO jdglVO) {
        //节点管理操作校验,校验出错时返回报错提示
        String erro = checkJdglVo(jdglVO);
        if (StringUtil.isNotBlank(erro)) {
            return R.fail(erro);
        }
        //当是否在时间轴显示字段为空时，默认显示在时间轴上
        jdglVO.setSjzXs(Optional.ofNullable(jdglVO.getSjzXs()).orElse(SHOW_ON_TIMELINE));
        boolean b = jdglService.updateById(jdglVO);
        return R.status(b);
    }

    /**
     * 抽离新增、修改公共校验部分
     *
     * @param jdglVO
     * @return
     */
    String checkJdglVo(ZlkJdglVO jdglVO) {
        if (Func.isEmpty(jdglVO) || StringUtil.isEmpty(jdglVO.getJdMc())) {
            return "节点名称不可为空";
        }
        if (Func.isEmpty(jdglVO) || StringUtil.isEmpty(jdglVO.getJdSj())) {
            return "节点时间不可为空";
        }
        QueryWrapper qwByName = new QueryWrapper<ZlkJdgl>();
        qwByName.eq("pjlx", jdglVO.getPjlx());
        qwByName.eq("jd_mc", jdglVO.getJdMc());
        if (jdglVO.getId() != null) {
            qwByName.ne("id", jdglVO.getId());
        }
        //校验节点名称是否重复
        int rows = jdglService.count(qwByName);
        if (rows > 0) {
            return "节点名称已存在";
        }

        QueryWrapper qwByTime = new QueryWrapper<ZlkJdgl>();
        qwByTime.eq("pjlx", jdglVO.getPjlx());
        qwByTime.eq("jd_sj", jdglVO.getJdSj());
        if (jdglVO.getId() != null) {
            qwByTime.ne("id", jdglVO.getId());
        }
        //校验节点时间是否重复
        int rowsByTime = jdglService.count(qwByTime);
        if (rowsByTime > 0) {
            return "相同的节点时间已存在";
        }
        return null;
    }


    /**
     * 删除 教学评价-资料库平台-节点管理表
     * 根据主键ID集合逻辑删除数据
     *
     * @param ids 主键集合
     * <AUTHOR> 作者
     * @since 2025-07-24 集合
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return jdglService.deleteByIds(Func.toLongList(ids));
    }


    /**
     * 导出Excel
     *
     * @param response    返回响应
     * @param fileName    文件名
     * @param sheetName   sheet页名称
     * @param columnNames 要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段
     * @param ids         要导出的id,多个id用逗号连接.如果为空,将导出全部数据
     * @param ascs        正排序字段,多个字段用逗号连接
     * @param descs       倒排序字段,多个字段用逗号连接
     * @param map         高级查询字段,请参考高级查询逻辑
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<ZlkJdgl> queryWrapper = Condition.getQueryWrapper(map, ZlkJdgl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<ZlkJdgl> list = jdglService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, ZlkJdgl.class);
    }


    /**
     * 导入Excel
     *
     * @param file 文件名
     * <AUTHOR> 作者
     * @since 2025-07-24 日期
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<ZlkJdgl> list = ExcelUtil.read(file, ZlkJdgl.class);
        //TODO 此处需要根据具体业务添加代码
        jdglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     *
     * @param response    返回的响应数据
     * @param columnNames 导入模板的字段
     * <AUTHOR> 作者
     * @since 2025-07-24 创建日期
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    public void template(HttpServletResponse response,
                         @ApiParam(value = "要导出的字段,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames) {
        QueryWrapper<ZlkJdgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<ZlkJdgl> list = jdglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        ExcelUtil.export(response, "Jdgl导入模板", "Jdgl导入模板", columnFiledNames, list, ZlkJdgl.class);
    }

    /**
     * 时间轴查询
     */
    @GetMapping("/listBySjz")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "时间轴列表查询", notes = "传入pjlx")
    public R listBySjz(@RequestParam Map<String, Object> map) {
        return R.data(jdglService.listBySjz(map));
    }
}
