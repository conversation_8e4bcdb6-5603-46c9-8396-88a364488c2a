<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zlkgl.mapper.FlglWjjMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flglWjjResultMap" type="com.xpaas.zlkgl.entity.FlglWjj">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="fj_wjj_id" property="fjWjjId"/>
        <result column="jd_id" property="jdId"/>
        <result column="flgl_lb" property="flglLb"/>
        <result column="pj_lx" property="pjLx"/>
        <result column="wjj_mc" property="wjjMc"/>
        <result column="wjj_lx" property="wjjLx"/>
        <result column="wblj_dz" property="wbljDz"/>
        <result column="wjj_px" property="wjjPx"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="flglWjjResultMapVO" type="com.xpaas.zlkgl.vo.FlglWjjVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="fj_wjj_id" property="fjWjjId"/>
        <result column="jd_id" property="jdId"/>
        <result column="flgl_lb" property="flglLb"/>
        <result column="pj_lx" property="pjLx"/>
        <result column="wjj_mc" property="wjjMc"/>
        <result column="wjj_lx" property="wjjLx"/>
        <result column="wblj_dz" property="wbljDz"/>
        <result column="wjj_px" property="wjjPx"/>
    </resultMap>

    <select id="selectFlglWjjPage" resultMap="flglWjjResultMapVO">
        select * from T_TD_JXPJ_ZLK_FLGL_WJJ where scbj = 0
    </select>

    <select id="countResourcesByPjLx" resultType="map">
        SELECT
        flgl_lb AS flglLb,
        SUM(cnt) AS totalCount,
        MAX(pj_lx) AS pjLx
        FROM (
        -- 1. 外链文件夹：wjj_lx = '0'，且 pj_lx 匹配，flgl_lb 有效
        SELECT
        flgl_lb,
        pj_lx,
        COUNT(*) AS cnt
        FROM t_td_jxpj_zlk_flgl_wjj
        WHERE SCBJ = 0
        AND wjj_lx = '0'
        AND pj_lx = #{pjLx}
        <if test="jdId != null and jdId != ''">
            AND jd_id = #{jdId}
        </if>
        AND flgl_lb IS NOT NULL
        AND TRIM(flgl_lb) != ''
        GROUP BY flgl_lb, pj_lx

        UNION ALL

        -- 2. 文件数量：关联到该 pj_lx 的文件夹下的文件
        SELECT
        wjj.flgl_lb,
        wjj.pj_lx,
        COUNT(*) AS cnt
        FROM t_td_jxpj_zlk_zlgl zl
        INNER JOIN t_td_jxpj_zlk_flgl_wjj wjj
        ON zl.wjj_id = wjj.ID
        WHERE zl.SCBJ = 0
        AND wjj.SCBJ = 0
        AND wjj.pj_lx = #{pjLx}
        <if test="jdId != null and jdId != ''">
            AND wjj.jd_id = #{jdId}
        </if>
        AND wjj.flgl_lb IS NOT NULL
        AND TRIM(wjj.flgl_lb) != ''
        GROUP BY wjj.flgl_lb, wjj.pj_lx
        ) AS combined
        GROUP BY flgl_lb;
    </select>
</mapper>
