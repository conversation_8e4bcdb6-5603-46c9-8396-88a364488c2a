package com.xpaas.zlkgl.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教学评价-资料库平台-回收站表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("T_TD_JXPJ_ZLK_HSZ")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Hsz对象", description = "教学评价-资料库平台-回收站表")
public class Hsz extends TenantEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 原始表记录ID
     */
    @ExcelProperty("原始表记录ID")
    @ApiModelProperty(value = "原始表记录ID")
    private String ysbId;

    /**
     * 原始表类型(flgl_wjj/wxzl_wjj/zlgl)
     */
    @ExcelProperty("原始表类型(flgl_wjj/wxzl_wjj/zlgl)")
    @ApiModelProperty(value = "原始表类型(flgl_wjj/wxzl_wjj/zlgl)")
    private String ysbLx;

    /**
     * 显示名称
     */
    @ExcelProperty("显示名称")
    @ApiModelProperty(value = "显示名称")
    private String xsmc;

    /**
     * 删除批次ID(同一次删除操作的记录相同)
     */
    @ExcelProperty("删除批次ID(同一次删除操作的记录相同)")
    @ApiModelProperty(value = "删除批次ID(同一次删除操作的记录相同)")
    private String scPcId;

    /**
     * 删除类型(1:主动删除 2:级联删除)
     */
    @ExcelProperty("删除类型(1:主动删除 2:级联删除)")
    @ApiModelProperty(value = "删除类型(1:主动删除 2:级联删除)")
    private String scLx;

    /**
     * 删除时的父级ID
     */
    @ExcelProperty("删除时的父级ID")
    @ApiModelProperty(value = "删除时的父级ID")
    private String ysFjId;

    /**
     * 评价类型(所有类型都需要)
     */
    @ExcelProperty("评价类型(所有类型都需要)")
    @ApiModelProperty(value = "评价类型(所有类型都需要)")
    private String pjLx;

    /**
     * 节点id(仅flgl_wjj需要)
     */
    @ExcelProperty("节点id(仅flgl_wjj需要)")
    @ApiModelProperty(value = "节点id(仅flgl_wjj需要)")
    private String jdId;

    /**
     * 所属分类管理类别(仅flgl_wjj需要)
     */
    @ExcelProperty("所属分类管理类别(仅flgl_wjj需要)")
    @ApiModelProperty(value = "所属分类管理类别(仅flgl_wjj需要)")
    private String flglLb;


}
