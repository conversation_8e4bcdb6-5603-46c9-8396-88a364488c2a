package com.xpaas.core.permission.checker;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.tenant.mp.TenantEntity;
import com.xpaas.core.tool.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 数据权限检查器
 * <p>
 * 实现简单的权限控制：
 * 1. 创建者可以删除/修改自己的数据
 * 2. 管理员可以删除/修改所有数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataPermissionChecker extends BasePermissionChecker {

    /**
     * 根据实体类和ID获取实体对象
     */
    private static TenantEntity getEntityById(Class<?> entityClass, Long id) {
        try {
            // 获取对应的Mapper Bean
            String mapperBeanName = getMapperBeanName(entityClass);
            BaseMapper<?> mapper = SpringUtil.getBean(mapperBeanName);

            if (mapper != null) {
                return (TenantEntity) mapper.selectById(id);
            }

            log.warn("未找到对应的Mapper: {}", mapperBeanName);
            return null;

        } catch (Exception e) {
            log.error("获取实体数据异常，entityClass: {}, id: {}", entityClass.getSimpleName(), id, e);
            return null;
        }
    }

    /**
     * 根据实体类名获取Mapper Bean名称
     * 例如: WxzlWjj -> wxzlWjjMapper
     */
    private static String getMapperBeanName(Class<?> entityClass) {
        String className = entityClass.getSimpleName();
        return className.substring(0, 1).toLowerCase() + className.substring(1) + "Mapper";
    }

    /**
     * 检查单条数据权限
     */
    private static boolean checkSingleDataPermission(Class<?> entityClass, Long id, LoginUser currentUser, String operation) {
        try {
            // 获取实体数据
            TenantEntity entity = getEntityById(entityClass, id);
            if (entity == null) {
                log.warn("数据不存在，ID: {}", id);
                return false;
            }

            Long creatorId = entity.getCjr();
            if (creatorId == null) {
                log.warn("数据创建人为空，ID: {}", id);
                return false;
            }

            // 判断是否为数据创建人本人
            return Objects.equals(creatorId, currentUser.getUserId());

        } catch (Exception e) {
            log.error("检查单条数据权限异常，ID: {}", id, e);
            return false;
        }
    }

    /**
     * 应用空结果过滤（安全兜底）
     */
    private static <T extends TenantEntity> void applyEmptyResult(Wrapper<T> wrapper) {
        if (wrapper instanceof LambdaQueryWrapper) {
            ((LambdaQueryWrapper<T>) wrapper).eq(TenantEntity::getId, -1L);
        } else if (wrapper instanceof QueryWrapper) {
            ((QueryWrapper<T>) wrapper).eq("id", -1L);
        }
    }

    /**
     * 应用用户过滤条件
     */
    private static <T extends TenantEntity> void applyUserFilter(Wrapper<T> wrapper, Long userId) {
        if (wrapper instanceof LambdaQueryWrapper) {
            ((LambdaQueryWrapper<T>) wrapper).eq(TenantEntity::getCjr, userId);
        } else if (wrapper instanceof QueryWrapper) {
            ((QueryWrapper<T>) wrapper).eq("cjr", userId);
        }
    }

    /**
     * 检查数据权限
     *
     * @param entityClass 实体类
     * @param ids         ID列表
     * @param operation   操作类型
     * @return 是否有权限
     */
    public boolean checkPermission(Class<?> entityClass, List<Long> ids, String operation) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                log.warn("未获取到当前登录用户信息");
                return false;
            }

            // 1. 判断当前用户是否为管理员
            if (isAdmin(currentUser.getRoleId())) {
                return true;
            }

            // 2. 检查每条数据的权限
            for (Long id : ids) {
                if (!checkSingleDataPermission(entityClass, id, currentUser, operation)) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("权限检查异常", e);
            return false;
        }
    }



    /**
     * 为查询添加数据范围过滤
     * 非管理员只能查看自己创建的数据
     *
     * @param wrapper 查询条件
     * @param <T>     实体类型
     */
    public <T extends TenantEntity> void applyDataScope(Wrapper<T> wrapper) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                // 未登录用户不能查看任何数据
                applyEmptyResult(wrapper);
                return;
            }

            // 管理员可以查看所有数据
            if (isAdmin(currentUser.getRoleId())) {
                return;
            }

            // 非管理员只能查看自己创建的数据
            applyUserFilter(wrapper, currentUser.getUserId());

        } catch (Exception e) {
            log.error("应用数据范围过滤异常", e);
            // 出现异常时，为了安全起见，不显示任何数据
            applyEmptyResult(wrapper);
        }
    }
}
