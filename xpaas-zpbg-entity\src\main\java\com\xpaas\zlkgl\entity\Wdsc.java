package com.xpaas.zlkgl.entity;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelProperty;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xpaas.core.tenant.mp.TenantEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 教学评价-资料库平台-我的收藏表实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_TD_JXPJ_ZLK_WDSC")
@ApiModel(value = "Wdsc对象", description = "教学评价-资料库平台-我的收藏表")
public class Wdsc extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 数据id
	*/
	@ExcelProperty("数据id")
	@ApiModelProperty(value = "数据id")
	private String sjId;



}
