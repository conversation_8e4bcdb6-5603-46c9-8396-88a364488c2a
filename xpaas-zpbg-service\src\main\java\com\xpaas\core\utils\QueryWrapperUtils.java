package com.xpaas.core.utils;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * QueryWrapper 帮助类
 *
 * <AUTHOR>
 * @since 2025/08/05 17:11
 **/
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class QueryWrapperUtils {

    /**
     * 添加默认排序条件（自定义排序字段名，创建时间字段名固定为cjrq）
     * 按指定字段升序排序，相同值时按创建时间降序排序
     *
     * @param <T>          实体类型
     * @param queryWrapper 查询条件包装器
     * @param pxField      排序字段名（如：wjj_px、zl_px等）
     * @return 添加排序条件后的QueryWrapper
     */
    public static <T> QueryWrapper<T> addDefaultOrder(QueryWrapper<T> queryWrapper, String pxField) {
        return queryWrapper.orderByAsc(pxField).orderByDesc("cjrq");
    }

    /**
     * 添加默认排序条件（使用默认字段名）
     * 默认按 wjj_px 升序，cjrq 降序排序
     *
     * @param <T>          实体类型
     * @param queryWrapper 查询条件包装器
     * @return 添加排序条件后的QueryWrapper
     */
    public static <T> QueryWrapper<T> addDefaultOrder(QueryWrapper<T> queryWrapper) {
        return queryWrapper.orderByAsc("wjj_px").orderByDesc("cjrq");
    }
}