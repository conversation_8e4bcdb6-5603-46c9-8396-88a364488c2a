<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xpaas.zlkgl.mapper.WdscMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="wdscResultMap" type="com.xpaas.zlkgl.entity.Wdsc">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="sj_id" property="sjId"/>
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="wdscResultMapVO" type="com.xpaas.zlkgl.vo.WdscVO">
        <result column="ID" property="id"/>
        <result column="ZHID" property="zhid"/>
        <result column="CJR" property="cjr"/>
        <result column="CJBM" property="cjbm"/>
        <result column="CJDW" property="cjdw"/>
        <result column="CJRQ" property="cjrq"/>
        <result column="GXR" property="gxr"/>
        <result column="GXRQ" property="gxrq"/>
        <result column="SCBJ" property="scbj"/>
        <result column="ZT" property="zt"/>
        <result column="sj_id" property="sjId"/>

        <result column="zl_mc" property="zlMc"/>
        <result column="zl_cmm" property="zlCmm"/>
        <result column="zl_dz" property="zlDz"/>
        <result column="wxzl_lx" property="wxzlLx"/>
        <result column="pj_lx" property="pjLx"/>
        <result column="zl_mj" property="zlMj"/>
        <result column="wj_dx" property="wjDx"/>
        <result column="wj_gs" property="wjGs"/>
        <result column="wjj_id" property="wjjId"/>
    </resultMap>

    <select id="selectWdscPage" resultMap="wdscResultMapVO">
        SELECT
            zl.zl_mc,
            zl.zl_cmm,
            (case when zl.zl_mj = 1 then '机密文件' else zl.zl_dz end) zl_dz,
            zl.wxzl_lx,
            zl.pj_lx,
            zl.zl_mj,
            zl.wj_dx,
            zl.wj_gs,
            zl.wjj_id,
            sc.GXRQ
        FROM
            T_TD_JXPJ_ZLK_WDSC sc
            LEFT JOIN t_td_jxpj_zlk_zlgl zl ON sc.sj_id = zl.id
        WHERE
            sc.scbj = 0
            AND zl.SCBJ = 0
        <if test="map.cjr != null and map.cjr !=''">
            AND sc.CJR = #{map.cjr}
        </if>
        <if test="map.zlMc != null and map.zlMc !=''">
            AND (zl.zl_mc like concat('%',#{map.zlMc},'%') or zl.zl_cmm like concat('%',#{map.zlMc},'%'))
        </if>
        <if test="map.sjId != null and map.sjId !=''">
            AND sc.sj_id = #{sjId}
        </if>
    </select>

</mapper>
