package com.xpaas.zlkgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.xpaas.core.boot.ctrl.BaseController;
import com.xpaas.core.excel.util.ExcelUtil;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.mp.support.Query;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.enums.FolderCategoryEnum;
import com.xpaas.zlkgl.service.ICopyService;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.vo.ZlglVO;
import com.xpaas.zlkgl.wrapper.ZlglWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

/**
 * 教学评价-资料库平台-资料管理表 控制器
 * controller 入口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/zlgl")
@Api(value = "教学评价-资料库平台-资料管理表", tags = "教学评价-资料库平台-资料管理表接口")
public class ZlglController extends BaseController {

    //资料管理构造器接口
    private ZlglWrapper zlglWrapper;
    //资料管理接口
    private IZlglService zlglService;
    //复制服务接口
    private ICopyService copyService;
    //分类管理文件夹接口
    private IFlglWjjService iFlglWjjService;
    //外校资料文件夹接口
    private IWxzlWjjService iWxzlWjjService;

    // 默认密级 为秘密
    private static final String DEFAULT_SECRET_LEVEL = "0";
    //默认排序为 1
    private static final Integer DEFAULT_SORT = 1;

    /**
     * 获取详情数据
     *
     * @param zlgl zlgl实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入zlgl")
    public R<ZlglVO> detail(Zlgl zlgl) {
        Zlgl detail = zlglService.getOne(Condition.getQueryWrapper(zlgl));
        return R.data(zlglWrapper.entityVO(detail));
    }

    /**
     * 根据主键集合查询 教学评价-资料库平台-资料管理表 数据
     *
     * @param ids 主键集合
     * <AUTHOR>  作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/listByIds")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "根据IDs查询", notes = "传入ids")
    public R<List<ZlglVO>> listByIds(String ids) {
        List<Zlgl> listByIds = zlglService.listByIds(Func.toLongList(ids));
        return R.data(zlglWrapper.listVO(listByIds));
    }

    /**
     * 根据条件查询 教学评价-资料库平台-资料管理表 数据
     *
     * @param zlgl zlgl实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "列表", notes = "传入zlgl")
    public R<List<ZlglVO>> list(Zlgl zlgl) {
        List<Zlgl> lists = zlglService.list(Condition.getQueryWrapper(zlgl));
        return R.data(zlglWrapper.listVO(lists));
    }

    /**
     * 分页查询 教学评价-资料库平台-资料管理表数据
     *
     * @param zlgl  zlgl实体
     * @param query 查询条件
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入zlgl")
    public R<IPage<ZlglVO>> page(ZlglVO zlgl, Query query) {
        IPage<Zlgl> pages = zlglService.page(Condition.getPage(query), Condition.getQueryWrapper(zlgl));
        return R.data(zlglWrapper.pageVO(pages));
    }

    /**
     * 高级查询，界面字段的高级搜索
     * (开发过程中根据需求调整精确查询或模糊查询)
     *
     * @param map   传递查询字段
     * @param query 查询分页条件
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/search")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "高级查询", notes = "传入 文件id、类型、名称")
    public R<IPage<ZlglVO>> search(@RequestParam Map<String, Object> map, Query query) {
        IPage<ZlglVO> pageVO = null;
        String wjjId = map.get("wjjId").toString();
        String wxzlLx = map.get("wxzlLx").toString();
        //校验文件夹id、外校资料类型不为空时查询
        if (!StringUtil.isEmpty(wjjId) && StringUtil.hasText(wxzlLx)) {
            //赋值查询字段及查询条件
            QueryWrapper<Zlgl> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("ID", "wjj_id", "zl_mc", "zl_cmm", "zl_px", "(case when zl_mj = 1 then '机密文件' else zl_dz end) zl_dz", "zl_mj", "wxzl_lx", "pj_lx", "wj_gs", "wj_dx");
            queryWrapper.eq("wjj_id", wjjId);
            queryWrapper.eq("wxzl_lx", wxzlLx);
            //根据名称检索时，携带重命名检索
            if (!StringUtil.isEmpty(map.get("zlMc"))) {
                queryWrapper.nested(qw -> qw.like("zl_mc", map.get("zlMc")).or().like("zl_cmm", map.get("zlMc")));
            }
            queryWrapper.orderByAsc("zl_px");
            IPage<Zlgl> pages = zlglService.page(Condition.getPage(query), queryWrapper);
            pageVO = zlglWrapper.pageVO(pages);
        }
        return R.data(pageVO);
    }


    /**
     * 新增 教学评价-资料库平台-资料管理表
     *
     * @param zlglVO zlglVO实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入zlgl")
    public R save(@Valid @RequestBody ZlglVO zlglVO) {
        //资料管理操作校验,校验出错时返回报错提示
        String erro = checkZlglVO(zlglVO);
        if (StringUtil.isNotBlank(erro)) {
            return R.fail(erro);
        }
        //当资料密级字段为空时，默认秘密
        if (StringUtil.isEmpty(zlglVO.getZlMj().trim())) {
            zlglVO.setZlMj(DEFAULT_SECRET_LEVEL);
        }
        //当资料排序字段为空时，默认1
        zlglVO.setZlPx(Optional.ofNullable(zlglVO.getZlPx()).orElse(DEFAULT_SORT));
        boolean b = zlglService.save(zlglVO);
        return R.status(b);
    }

    /**
     * 修改 教学评价-资料库平台-资料管理表
     * 根据主键ID修改数据
     *
     * @param zlglVO zlglVO实体
     * <AUTHOR>
     * @since 2025-07-25
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入zlgl")
    public R update(@Valid @RequestBody ZlglVO zlglVO) {
        //资料管理操作校验,校验出错时返回报错提示
        String erro = checkZlglVO(zlglVO);
        if (StringUtil.isNotBlank(erro)) {
            return R.fail(erro);
        }
        //校验机密文件地址
        if ((Func.isNotEmpty(zlglVO) && !StringUtil.isEmpty(zlglVO.getZlDz())) && "机密文件".equals(zlglVO.getZlDz())) {
            zlglVO.setZlDz(null);
        }
        //当资料密级字段为空时，默认秘密
        if (StringUtil.isEmpty(zlglVO.getZlMj().trim())) {
            zlglVO.setZlMj(DEFAULT_SECRET_LEVEL);
        }
        //当资料排序字段为空时，默认1
        zlglVO.setZlPx(Optional.ofNullable(zlglVO.getZlPx()).orElse(DEFAULT_SORT));
        boolean b = zlglService.updateById(zlglVO);
        return R.status(b);
    }

    /**
     * 抽离新增、修改公共校验部分
     *
     * @param zlglVO
     * @return
     */
    String checkZlglVO(ZlglVO zlglVO) {
        if (Func.isEmpty(zlglVO) || StringUtil.isEmpty(zlglVO.getZlMc())) {
            return "资料名称不可为空";
        }
        if (Func.isEmpty(zlglVO) || StringUtil.isEmpty(zlglVO.getZlDz())) {
            return "资料文件未上传";
        }
        //校验同一文件夹下同名资料
        QueryWrapper<Zlgl> qw = new QueryWrapper<>();
        qw.eq("wjj_id", zlglVO.getWjjId());
        qw.eq("zl_mc", zlglVO.getZlMc());
        if (zlglVO.getId() != null) {
            qw.ne("id", zlglVO.getId());
        }
        int rowsByName = zlglService.count(qw);
        if (rowsByName > 0) {
            return "当前文件夹下已存在同名资料";
        }
        //校验外校资料文件夹
        if ("1".equals(zlglVO.getWxzlLx())) {
            //文件夹id无效时提示
            int rows = iWxzlWjjService.count(new LambdaQueryWrapper<WxzlWjj>().eq(WxzlWjj::getId, zlglVO.getWjjId()));
            if (rows == 0) {
                return "文件夹无效";
            }
            //文件夹为外链类型时提示
            int rowsByOutside = iWxzlWjjService.count(new LambdaQueryWrapper<WxzlWjj>().eq(WxzlWjj::getId, zlglVO.getWjjId()).eq(WxzlWjj::getWjjLx, FolderCategoryEnum.OUTSIDE_LINK.getCode()));
            if (rowsByOutside > 0) {
                return "外链文件夹内无法添加资料文件";
            }
        } else {
            //校验分类管理文件夹
            //文件夹id无效时提示
            int rows = iFlglWjjService.count(new LambdaQueryWrapper<FlglWjj>().eq(FlglWjj::getId, zlglVO.getWjjId()));
            if (rows == 0) {
                return "文件夹无效";
            }
            //文件夹为外链类型时提示
            int rowsByOutside = iFlglWjjService.count(new LambdaQueryWrapper<FlglWjj>().eq(FlglWjj::getId, zlglVO.getWjjId()).eq(FlglWjj::getWjjLx, FolderCategoryEnum.OUTSIDE_LINK.getCode()));
            if (rowsByOutside > 0) {
                return "外链文件夹内无法添加资料文件";
            }
        }
        return null;
    }

    /**
     * 新增或修改 教学评价-资料库平台-资料管理表 (优先使用save或update接口)
     * id存在的情况下进行更新操作，id不存在进行插入操作
     *
     * @param zlgl zlgl实体
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入zlgl")
    public R submit(@Valid @RequestBody Zlgl zlgl) {
        return R.status(zlglService.saveOrUpdate(zlgl));
    }


    /**
     * 删除 教学评价-资料库平台-资料管理表
     * 根据主键ID集合逻辑删除数据
     *
     * @param ids 主键集合
     * <AUTHOR> 作者
     * @since 2025-07-25 集合
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        boolean b = zlglService.deleteLogic(Func.toLongList(ids));
        return R.status(b);
    }

    /**
     * 复制文件
     * 复制指定文件到目标文件夹（文件表是共用的，不区分文件夹类型）
     *
     * @param fileId         源文件ID
     * @param targetFolderId 目标文件夹ID
     * @return 复制结果
     * <AUTHOR>
     * @since 2025-07-30
     */
    @PostMapping("/copyFile")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "复制文件", notes = "传入源文件ID和目标文件夹ID")
    public R copyFile(@ApiParam(value = "源文件ID", required = true) @RequestParam String fileId,
                      @ApiParam(value = "目标文件夹ID", required = true) @RequestParam String targetFolderId) {
        return R.status(copyService.copyFile(fileId, targetFolderId));
    }


    /**
     * 导出Excel
     *
     * @param response    返回响应
     * @param fileName    文件名
     * @param sheetName   sheet页名称
     * @param columnNames 要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段
     * @param ids         要导出的id,多个id用逗号连接.如果为空,将导出全部数据
     * @param ascs        正排序字段,多个字段用逗号连接
     * @param descs       倒排序字段,多个字段用逗号连接
     * @param map         高级查询字段,请参考高级查询逻辑
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @GetMapping("/export")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    public void exportExcel(HttpServletResponse response,
                            @ApiParam(value = "文件名", required = true) @RequestParam("fileName") String fileName,
                            @ApiParam(value = "sheet页名称") String sheetName,
                            @ApiParam(value = "要导出的字段名,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames,
                            @ApiParam(value = "要导出的id,多个id用逗号连接.如果为空,将导出全部数据") String ids,
                            @ApiParam(value = "正排序字段,多个字段用逗号连接") String ascs,
                            @ApiParam(value = "倒排序字段,多个字段用逗号连接") String descs,
                            @ApiParam(value = "高级查询字段,请参考高级查询逻辑") @RequestParam Map<String, Object> map) {
        //剔除非实体类字段
        map.remove("fileName");
        map.remove("sheetName");
        map.remove("columnNames");
        map.remove("ids");
        map.remove("ascs");
        map.remove("descs");
        QueryWrapper<Zlgl> queryWrapper = Condition.getQueryWrapper(map, Zlgl.class);
        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        //指定id
        if (StringUtil.isNotBlank(ids) && ids.split(",").length > 0) {
            queryWrapper.in("id", Arrays.asList(ids.split(",")));
        }
        //正排序
        if (StringUtil.isNotBlank(ascs) && ascs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(ascs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByAsc(tmpList);
        }
        //倒排序
        if (StringUtil.isNotBlank(descs) && descs.split(",").length > 0) {
            String[] tmpList = Func.toStrArray(descs);
            for (int i = 0; i < tmpList.length; i++) {
                tmpList[i] = StringUtil.humpToUnderline(tmpList[i]);
            }
            queryWrapper.orderByDesc(tmpList);
        }
        //设置sheetName
        if (StringUtil.isBlank(sheetName)) {
            sheetName = fileName;
        }
        List<Zlgl> list = zlglService.list(queryWrapper);
        ExcelUtil.export(response, fileName, sheetName, columnFiledNames, list, Zlgl.class);
    }


    /**
     * 导入Excel
     *
     * @param file 文件名
     * <AUTHOR> 作者
     * @since 2025-07-25 日期
     */
    @PostMapping("/import")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        List<Zlgl> list = ExcelUtil.read(file, Zlgl.class);
        //TODO 此处需要根据具体业务添加代码
        zlglService.saveBatch(list);
        return R.status(true);
    }

    /**
     * 下载导入模板
     *
     * @param response    返回的响应数据
     * @param columnNames 导入模板的字段
     * <AUTHOR> 作者
     * @since 2025-07-25 创建日期
     */
    @GetMapping("/template")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "下载导入模板", notes = "下载导入模板")
    public void template(HttpServletResponse response,
                         @ApiParam(value = "要导出的字段,多个字段用逗号连接.如果为空,将导出全部字段") String columnNames) {
        QueryWrapper<Zlgl> queryWrapper = new QueryWrapper<>();
        queryWrapper.last("limit 1");
        List<Zlgl> list = zlglService.list(queryWrapper);
        //TODO 此处需要根据具体业务添加代码

        //要导出的字段列表
        List<String> columnFiledNames = new ArrayList<>();
        if (StringUtil.isNotBlank(columnNames) && columnNames.split(",").length > 0) {
            columnFiledNames = Arrays.asList(columnNames.split(","));
        }
        ExcelUtil.export(response, "Zlgl导入模板", "Zlgl导入模板", columnFiledNames, list, Zlgl.class);
    }
}
