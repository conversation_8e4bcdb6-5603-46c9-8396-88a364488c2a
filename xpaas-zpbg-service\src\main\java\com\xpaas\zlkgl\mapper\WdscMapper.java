package com.xpaas.zlkgl.mapper;

import com.xpaas.core.mp.support.Query;
import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

/**
 * 教学评价-资料库平台-我的收藏表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Repository
public interface WdscMapper extends BaseMapper<Wdsc> {

	/**
	 * 自定义分页
	 * 分页查询教学评价-资料库平台-我的收藏表表数据
	 * @param page
	 * @param map
	 * <AUTHOR>
	 * @since 2025-08-04
	 * @return
	 */
	List<WdscVO> selectWdscPage(IPage page, Map<String, Object> map);

}
