package com.xpaas.zlkgl.wrapper;

import com.xpaas.core.mp.support.BaseEntityWrapper;
import com.xpaas.core.tool.utils.BeanUtil;
import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import java.util.Objects;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
/**
 * 教学评价-资料库平台-我的收藏表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Component
public class WdscWrapper extends BaseEntityWrapper<Wdsc, WdscVO>  {


	/**
	* 将entity转换成 entityVO
	 * <AUTHOR>
	 * @since 2025-08-04
    * @return 转换后的entityVO对象
    */
	@Override
	public WdscVO entityVO(Wdsc wdsc) {
		WdscVO wdscVO = Objects.requireNonNull(BeanUtil.copy(wdsc, WdscVO.class));
		//User cjr = UserCache.getUser(wdsc.getCjr());
		//if (cjr != null){
		//	wdscVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(wdsc.getGxr());
		//if (gxr != null){
		//	wdscVO.setGxrName(gxr.getName());
		//}
/**  **/
		return wdscVO;
	}





    @Override
    public WdscVO wrapperVO(WdscVO wdscVO) {
		//User cjr = UserCache.getUser(wdscVO.getCjr());
		//if (cjr != null){
		//	wdscVO.setCjrName(cjr.getName());
		//}
		//User gxr = UserCache.getUser(wdscVO.getGxr());
		//if (gxr != null){
		//	wdscVO.setGxrName(gxr.getName());
		//}
/**  */
        return wdscVO;
    }

}
