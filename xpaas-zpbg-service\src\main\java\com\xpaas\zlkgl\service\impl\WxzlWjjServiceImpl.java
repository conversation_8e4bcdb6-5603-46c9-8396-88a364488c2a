package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.permission.annotation.DataPermission;
import com.xpaas.core.permission.enums.OperationType;
import com.xpaas.core.utils.QueryWrapperUtils;
import com.xpaas.core.utils.TreeUtils;
import com.xpaas.core.utils.UrlUtils;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.WxzlWjjMapper;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.vo.WxzlWjjVO;
import com.xpaas.zlkgl.wrapper.WxzlWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 教学评价-资料库平台-外校资料文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxzlWjjServiceImpl extends BaseServiceImpl<WxzlWjjMapper, WxzlWjj> implements IWxzlWjjService {

    private final WxzlWjjWrapper wxzlWjjWrapper;
    private final IZlglService zlglService;

    @Override
    public IPage<WxzlWjjVO> selectWxzlWjjPage(IPage<WxzlWjjVO> page, WxzlWjjVO wxzlWjj) {
        return page.setRecords(baseMapper.selectWxzlWjjPage(page, wxzlWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<WxzlWjjVO> listTree(WxzlWjjVO bo) {
        QueryWrapper<WxzlWjj> queryWrapper = Condition.getQueryWrapper(bo);
        queryWrapper = QueryWrapperUtils.addDefaultOrder(queryWrapper);
        List<WxzlWjjVO> vos = wxzlWjjWrapper.listVO(baseMapper.selectList(queryWrapper));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                WxzlWjjVO::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(WxzlWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getWbljDz()) && !UrlUtils.isValidHttpUrl(vo.getWbljDz())) {
            throw new ServiceException("外部链接地址必须是以 http:// 或 https:// 开头的合法网址");
        }

        if (StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 重名校验：同一父级下的文件夹名称不能重复
        checkDuplicateName(vo.getFjWjjId(), vo.getWjjMc(), null);

        //  转换为实体类并保存
        return save(wxzlWjjWrapper.wrapperVO(vo));
    }

    /**
     * 修改
     */
    @Override
    @DataPermission(entityClass = WxzlWjj.class, operation = OperationType.UPDATE, message = "无权限修改该文件夹")
    public boolean update(WxzlWjjVO vo) {

        // 1. 获取原数据
        WxzlWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            if (StringUtils.hasText(vo.getWbljDz()) && !UrlUtils.isValidHttpUrl(vo.getWbljDz())) {
                throw new ServiceException("外部链接地址必须是以 http:// 或 https:// 开头的合法网址");
            }
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            WxzlWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WxzlWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 重名校验：如果名称或父级变化，则检查是否冲突
        if (!Objects.equals(vo.getWjjMc(), oldEntity.getWjjMc()) ||
                !Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId())) {
            checkDuplicateName(vo.getFjWjjId(), vo.getWjjMc(), String.valueOf(vo.getId()));
        }

        // 6. 更新数据
        return baseMapper.updateById(wxzlWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 校验同级目录下是否存在同名文件夹
     *
     * @param fjWjjId   父级ID，可以为null或空字符串，表示根目录
     * @param wjjMc     文件夹名称
     * @param excludeId 要排除的ID（用于更新时）
     */
    private void checkDuplicateName(String fjWjjId, String wjjMc, String excludeId) {
        LambdaQueryWrapper<WxzlWjj> queryWrapper = new LambdaQueryWrapper<>();

        // 处理父级为空的情况（根目录）
        if (StringUtils.hasText(fjWjjId)) {
            queryWrapper.eq(WxzlWjj::getFjWjjId, fjWjjId);
        } else {
            queryWrapper.and(qw -> qw.isNull(WxzlWjj::getFjWjjId).or().eq(WxzlWjj::getFjWjjId, ""));
        }

        queryWrapper.eq(WxzlWjj::getWjjMc, wjjMc);

        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(WxzlWjj::getId, excludeId);
        }

        long count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new ServiceException("当前目录下已存在同名文件夹：" + wjjMc);
        }
    }

    /**
     * 删除
     */
    @Override
    @DataPermission(entityClass = WxzlWjj.class, operation = OperationType.DELETE, message = "无权限删除该文件夹")
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            WxzlWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<WxzlWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(WxzlWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }
}
