package com.xpaas.zlkgl.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.permission.annotation.DataPermission;
import com.xpaas.core.permission.enums.OperationType;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.ZlkJdgl;
import com.xpaas.zlkgl.mapper.ZlkJdglMapper;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IZlkJdglService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 教学评价-资料库平台-节点管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
public class ZlkJdglServiceImpl extends BaseServiceImpl<ZlkJdglMapper, ZlkJdgl> implements IZlkJdglService {

    @Autowired
    private IFlglWjjService iFlgWjjService;

    /**
     * 时间轴查询
     */
    @Override
    public List<Map<String, Object>> listBySjz(Map<String, Object> map) {
        List<Map<String, Object>> listBySjz = new ArrayList<>();
        try {
            //按照节点时间的开始时间倒序获取节点列表
            List<ZlkJdgl> jdListByJdsjAsc = baseMapper.selectList(new LambdaQueryWrapper<ZlkJdgl>().eq(ZlkJdgl::getPjlx, map.get("pjLx")).eq(ZlkJdgl::getSjzXs, 1).last(true, "ORDER BY STR_TO_DATE(SUBSTRING_INDEX(jd_sj, '-', 1), '%Y/%m/%d') asc"));
            //存储年份信息
            Integer year = null;
            //判断列表第一条数据节点时间不为空时，赋值年份
            if (Func.isNotEmpty(jdListByJdsjAsc) && StringUtil.isNotBlank(jdListByJdsjAsc.get(0).getJdSj())) {
                String dateStr = jdListByJdsjAsc.get(0).getJdSj().split("-")[0];
                Date date = DateUtil.parse(dateStr, "yyyy/MM/dd");
                //获得年的部分
                year = DateUtil.year(date);
            }
            //当前年内的所有节点
            List<ZlkJdgl> jdListByYear = new ArrayList<>();
            //遍历节点列表
            for (int i = 0; i < jdListByJdsjAsc.size(); i++) {
                ZlkJdgl zlkJdgl = jdListByJdsjAsc.get(i);
                //节点时间不为空时获取当前年信息
                if (StringUtil.isNotBlank(zlkJdgl.getJdSj())) {
                    String dateStr = zlkJdgl.getJdSj().split("-")[0];
                    Date date = DateUtil.parse(dateStr, "yyyy/MM/dd");
                    //获得年的部分
                    Integer currentYear = DateUtil.year(date);
                    Boolean yearDiffFlag = !currentYear.equals(year);
                    jdListByYear.add(zlkJdgl);
                    if (yearDiffFlag) {
                        //如果不相等，移除最后一条
                        jdListByYear.remove(jdListByYear.size() - 1);
                        //年份数组，存储当前年的所有节点
                        Map<String, Object> yearMap = new HashMap<>();
                        //添加数据
                        yearMap.put(year.toString(), jdListByYear);
                        listBySjz.add(yearMap);
                        jdListByYear = new ArrayList<>();
                        //上一年数据添加完毕后加回数据
                        jdListByYear.add(zlkJdgl);
                        //赋值当前年份
                        year = currentYear;
                    }
                    //如果是最后一条
                    if (i == jdListByJdsjAsc.size() - 1) {
                        //年份数组，存储当前年的所有节点
                        Map<String, Object> yearMap = new HashMap<>();
                        //添加数据
                        yearMap.put(year.toString(), jdListByYear);
                        listBySjz.add(yearMap);
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            return listBySjz;
        }
    }

    /**
     * 根据ids删除节点
     *
     * @param ids
     * @return
     */
    @Override
    @DataPermission(entityClass = ZlkJdgl.class, operation = OperationType.DELETE, message = "无权限删除该节点信息")
    public R deleteByIds(List<Long> ids) {
        boolean isErro = false;
        String erroStr = "";
        try {
            for (Long id : ids) {
                String idStr = String.valueOf(id);
                //获取节点信息
                ZlkJdgl zlkJdgl = baseMapper.selectById(idStr);
                if (zlkJdgl == null) {
                    erroStr = "要删除的节点不存在";
                    isErro = true;
                }
                //检查是否存在子文件夹
                LambdaQueryWrapper<FlglWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
                folderQueryWrapper.eq(FlglWjj::getJdId, idStr);
                long folderCount = iFlgWjjService.getBaseMapper().selectCount(folderQueryWrapper);
                if (folderCount > 0) {
                    erroStr = "文件夹[" + zlkJdgl.getJdMc() + "]下存在子文件夹，不能删除";
                    isErro = true;
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            //无报错时执行逻辑删除
            return isErro ? R.fail(erroStr) : R.status(super.deleteLogic(ids));
        }

    }

}
