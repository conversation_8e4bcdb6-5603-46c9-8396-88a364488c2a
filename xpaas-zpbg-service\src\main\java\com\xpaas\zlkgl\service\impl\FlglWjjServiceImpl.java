package com.xpaas.zlkgl.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xpaas.core.log.exception.ServiceException;
import com.xpaas.core.mp.base.BaseServiceImpl;
import com.xpaas.core.mp.support.Condition;
import com.xpaas.core.permission.annotation.DataPermission;
import com.xpaas.core.permission.enums.OperationType;
import com.xpaas.core.utils.QueryWrapperUtils;
import com.xpaas.core.utils.TreeUtils;
import com.xpaas.core.utils.UrlUtils;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.Zlgl;
import com.xpaas.zlkgl.mapper.FlglWjjMapper;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IZlglService;
import com.xpaas.zlkgl.vo.FlglWjjVO;
import com.xpaas.zlkgl.wrapper.FlglWjjWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * 教学评价-资料库平台-分类管理文件夹树表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FlglWjjServiceImpl extends BaseServiceImpl<FlglWjjMapper, FlglWjj> implements IFlglWjjService {

    private final FlglWjjWrapper flglWjjWrapper;
    private final IZlglService zlglService;

    @Override
    public IPage<FlglWjjVO> selectFlglWjjPage(IPage<FlglWjjVO> page, FlglWjjVO flglWjj) {
        return page.setRecords(baseMapper.selectFlglWjjPage(page, flglWjj));
    }

    /**
     * 查询树结构
     */
    @Override
    public List<FlglWjjVO> listTree(FlglWjjVO bo) {
        QueryWrapper<FlglWjj> queryWrapper = Condition.getQueryWrapper(bo);
        queryWrapper = QueryWrapperUtils.addDefaultOrder(queryWrapper);
        queryWrapper.lambda().isNotNull(FlglWjj::getWjjMc).ne(FlglWjj::getWjjMc, "");
        List<FlglWjjVO> vos = flglWjjWrapper.listVO(baseMapper.selectList(queryWrapper));
        return TreeUtils.buildTree(
                vos,
                vo -> String.valueOf(vo.getId()),
                FlglWjj::getFjWjjId
        );
    }

    /**
     * 新增
     */
    @Override
    public boolean insert(FlglWjjVO vo) {

        String wjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
        vo.setWjjLx(wjjLx);

        if (StringUtils.hasText(vo.getWbljDz()) && !UrlUtils.isValidHttpUrl(vo.getWbljDz())) {
            throw new ServiceException("外部链接地址必须是以 http:// 或 https:// 开头的合法网址");
        }

        if (StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj parentFolder = baseMapper.selectById(vo.getFjWjjId());
            if (parentFolder == null) {
                throw new com.xpaas.core.log.exception.ServiceException("父级文件夹不存在");
            }
            if ("0".equals(parentFolder.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 重名校验：同一父级下的文件夹名称不能重复
        checkDuplicateName(vo.getFlglLb(), vo.getJdId(), vo.getFjWjjId(), vo.getWjjMc(), null);

        // 外部链接冲突校验：如果该分类下已有外部链接，则不允许创建文件夹
        if (StringUtils.hasText(vo.getWjjMc())) {
            LambdaQueryWrapper<FlglWjj> linkCheckWrapper = new LambdaQueryWrapper<>();
            linkCheckWrapper.eq(FlglWjj::getPjLx, vo.getPjLx())
                    .eq(FlglWjj::getJdId, vo.getJdId())
                    .eq(FlglWjj::getFlglLb, vo.getFlglLb())
                    .and(wrapper -> wrapper.isNull(FlglWjj::getWjjMc).or().eq(FlglWjj::getWjjMc, ""))
                    .eq(FlglWjj::getWjjLx, "0");
            if (baseMapper.selectCount(linkCheckWrapper) > 0) {
                throw new ServiceException("此所属分类管理类别已存在外部链接，不允许创建子文件夹");
            }
        }

        // 转换为实体类并保存
        return save(flglWjjWrapper.wrapperVO(vo));
    }


    /**
     * 修改
     */
    @Override
    @DataPermission(entityClass = FlglWjj.class, operation = OperationType.UPDATE, message = "无权限修改该文件夹")
    public boolean update(FlglWjjVO vo) {

        // 1. 获取原数据
        FlglWjj oldEntity = baseMapper.selectById(vo.getId());
        if (oldEntity == null) {
            throw new com.xpaas.core.log.exception.ServiceException("要修改的文件夹不存在");
        }

        // 2. 如果外部链接地址有变化，重新判断文件夹类型
        if (!Objects.equals(vo.getWbljDz(), oldEntity.getWbljDz())) {
            if (StringUtils.hasText(vo.getWbljDz()) && !UrlUtils.isValidHttpUrl(vo.getWbljDz())) {
                throw new ServiceException("外部链接地址必须是以 http:// 或 https:// 开头的合法网址");
            }
            String newWjjLx = StringUtils.hasText(vo.getWbljDz()) ? "0" : "1";
            vo.setWjjLx(newWjjLx);
        }

        // 3. 如果父级文件夹有变化，校验新的父级不能是外链
        if (!Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId()) && StringUtils.hasText(vo.getFjWjjId())) {
            FlglWjj newParent = baseMapper.selectById(vo.getFjWjjId());
            if (newParent == null) {
                throw new com.xpaas.core.log.exception.ServiceException("新的父级文件夹不存在");
            }
            if ("0".equals(newParent.getWjjLx())) {
                throw new com.xpaas.core.log.exception.ServiceException("外部链接下不允许创建子文件夹");
            }
        }

        // 4. 文件夹类型变更校验：确保没有子文件夹
        if (!Objects.equals(vo.getWjjLx(), oldEntity.getWjjLx())) {
            LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(FlglWjj::getFjWjjId, oldEntity.getId());
            long count = baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                throw new com.xpaas.core.log.exception.ServiceException("该文件夹下存在子文件夹，不能修改文件夹类型");
            }
        }

        // 5. 重名校验：如果名称或父级变化，则检查是否冲突
        if (!Objects.equals(vo.getWjjMc(), oldEntity.getWjjMc()) ||
                !Objects.equals(vo.getFjWjjId(), oldEntity.getFjWjjId())) {
            checkDuplicateName(vo.getFlglLb(), vo.getJdId(), vo.getFjWjjId(), vo.getWjjMc(), String.valueOf(vo.getId()));
        }

        // 6. 更新数据
        return baseMapper.updateById(flglWjjWrapper.wrapperVO(vo)) > 0;
    }

    /**
     * 校验同级目录下是否存在同名文件夹
     *
     * @param flglLb    所属分类
     * @param jdId      节点ID
     * @param fjWjjId   父级ID，可以为null或空字符串，表示根目录
     * @param wjjMc     文件夹名称
     * @param excludeId 要排除的ID（用于更新时）
     */
    private void checkDuplicateName(String flglLb, String jdId, String fjWjjId, String wjjMc, String excludeId) {
        LambdaQueryWrapper<FlglWjj> queryWrapper = new LambdaQueryWrapper<>();

        // 处理父级为空的情况（根目录）
        if (StringUtils.hasText(fjWjjId)) {
            queryWrapper.eq(FlglWjj::getFjWjjId, fjWjjId);
        } else {
            queryWrapper.and(qw -> qw.isNull(FlglWjj::getFjWjjId).or().eq(FlglWjj::getFjWjjId, ""));
        }

        queryWrapper.eq(FlglWjj::getWjjMc, wjjMc);
        queryWrapper.eq(FlglWjj::getJdId, jdId);
        queryWrapper.eq(FlglWjj::getFlglLb, flglLb);

        if (StringUtils.hasText(excludeId)) {
            queryWrapper.ne(FlglWjj::getId, excludeId);
        }

        long count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new ServiceException("当前目录下已存在同名文件夹：" + wjjMc);
        }
    }

    /**
     * 删除
     */
    @Override
    @DataPermission(entityClass = FlglWjj.class, operation = OperationType.DELETE, message = "无权限删除该文件夹")
    public boolean deleteByIds(List<Long> ids) {

        for (Long id : ids) {
            String idStr = String.valueOf(id);

            // 获取文件夹信息
            FlglWjj folder = baseMapper.selectById(idStr);
            if (folder == null) {
                throw new ServiceException("要删除的文件夹不存在");
            }

            // 1. 检查是否存在子文件夹
            LambdaQueryWrapper<FlglWjj> folderQueryWrapper = new LambdaQueryWrapper<>();
            folderQueryWrapper.eq(FlglWjj::getFjWjjId, idStr);
            long folderCount = baseMapper.selectCount(folderQueryWrapper);
            if (folderCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在子文件夹，不能删除");
            }

            // 2. 检查是否存在文件
            LambdaQueryWrapper<Zlgl> fileQueryWrapper = new LambdaQueryWrapper<>();
            fileQueryWrapper.eq(Zlgl::getWjjId, idStr);
            long fileCount = zlglService.count(fileQueryWrapper);
            if (fileCount > 0) {
                throw new ServiceException("文件夹[" + folder.getWjjMc() + "]下存在文件，不能删除");
            }
        }

        // 3. 执行逻辑删除
        return super.deleteLogic(ids);
    }

    /**
     * 统计节点下的各个评价类型资源数（文件 + 链接）
     */
    @Override
    public List<Map<String, Object>> countResourcesByPjLx(String pjLx, String jdId) {
        return baseMapper.countResourcesByPjLx(pjLx, jdId);
    }

    /**
     * 管理外部链接地址
     * 这是一个新增和修改通用的接口，主要管理外部链接地址
     *
     * @param vo 包含评价类型、节点ID、分类管理类别和外部链接地址的VO对象
     * @return 操作是否成功
     */
    @Override
    public boolean manageExternalLink(FlglWjjVO vo) {
        // 1. 参数校验
        String wbljDz = vo.getWbljDz();
        if (StringUtils.hasText(wbljDz) && !UrlUtils.isValidHttpUrl(wbljDz)) {
            throw new ServiceException("外部链接地址必须是以 http:// 或 https:// 开头的合法网址");
        }

        // 2. 公共查询条件：pjLx + jdId + flglLb
        Consumer<LambdaQueryWrapper<FlglWjj>> baseCondition = wrapper ->
                wrapper.eq(FlglWjj::getPjLx, vo.getPjLx())
                        .eq(FlglWjj::getJdId, vo.getJdId())
                        .eq(FlglWjj::getFlglLb, vo.getFlglLb());

        // 3. 查询是否已有外部链接记录（wjjMc 为空）
        LambdaQueryWrapper<FlglWjj> linkQueryWrapper = new LambdaQueryWrapper<>();
        baseCondition.accept(linkQueryWrapper);
        linkQueryWrapper.and(w -> w.isNull(FlglWjj::getWjjMc).or().eq(FlglWjj::getWjjMc, ""));
        FlglWjj existing = baseMapper.selectOne(linkQueryWrapper);

        // 4. 情况一：无外部链接记录
        if (existing == null) {
            if (StringUtils.hasText(wbljDz)) {
                // 检查该分类下是否已有其他文件夹
                LambdaQueryWrapper<FlglWjj> checkWrapper = new LambdaQueryWrapper<>();
                baseCondition.accept(checkWrapper);
                if (baseMapper.selectCount(checkWrapper) > 0) {
                    throw new ServiceException("此所属分类管理类别包含子文件夹，不允许创建外部链接");
                }
                return save(flglWjjWrapper.wrapperVO(vo));
            }
            return true;
        }

        // 5. 情况二：已有记录
        if (StringUtils.hasText(wbljDz)) {
            if (!Objects.equals(wbljDz, existing.getWbljDz())) {
                existing.setWbljDz(wbljDz);
                return updateById(existing);
            }
            return true;
        } else {
            return removeById(existing.getId());
        }
    }
}
