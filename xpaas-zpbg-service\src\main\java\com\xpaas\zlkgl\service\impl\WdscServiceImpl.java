package com.xpaas.zlkgl.service.impl;

import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.vo.WdscVO;
import com.xpaas.zlkgl.mapper.WdscMapper;
import com.xpaas.zlkgl.service.IWdscService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

/**
 * 教学评价-资料库平台-我的收藏表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class WdscServiceImpl extends BaseServiceImpl<WdscMapper, Wdsc> implements IWdscService {

	@Override
	public IPage<WdscVO> selectWdscPage(IPage<WdscVO> page, WdscVO wdsc) {
		return page.setRecords(baseMapper.selectWdscPage(page, wdsc));
	}

}
