package com.xpaas.zlkgl.service.impl;

import com.xpaas.core.mp.support.Query;
import com.xpaas.core.permission.checker.DataPermissionChecker;
import com.xpaas.core.secure.LoginUser;
import com.xpaas.core.secure.utils.AuthUtil;
import com.xpaas.core.tool.api.R;
import com.xpaas.core.tool.utils.Func;
import com.xpaas.core.tool.utils.StringUtil;
import com.xpaas.zlkgl.entity.FlglWjj;
import com.xpaas.zlkgl.entity.Wdsc;
import com.xpaas.zlkgl.entity.WxzlWjj;
import com.xpaas.zlkgl.entity.ZlkJdgl;
import com.xpaas.zlkgl.enums.FilesTypeEnum;
import com.xpaas.zlkgl.service.IFlglWjjService;
import com.xpaas.zlkgl.service.IWxzlWjjService;
import com.xpaas.zlkgl.service.IZlkJdglService;
import com.xpaas.zlkgl.vo.WdscVO;
import com.xpaas.zlkgl.mapper.WdscMapper;
import com.xpaas.zlkgl.service.IWdscService;
import com.xpaas.core.mp.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 教学评价-资料库平台-我的收藏表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class WdscServiceImpl extends BaseServiceImpl<WdscMapper, Wdsc> implements IWdscService {

	/**
	 * 管理员角色ID配置（从配置文件读取）
	 */
	@Value("${xpaas.roles.adminRoleId:}")
	private String adminRoleIds;

	@Value("${xpaas.roles.zlkGlyRoleId:}")
	private String zlkGlyRoleId;

	//分类管理文件夹接口
	@Autowired
	private IFlglWjjService iFlglWjjService;
	//节点管理接口
	@Autowired
	private IZlkJdglService jdglService;

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param map  教学评价-资料库平台-我的收藏表 实体
	 * @return
	 * <AUTHOR>
	 * @since 2025-08-04
	 */
	@Override
	public R<IPage<WdscVO>> selectWdscPage(IPage<WdscVO> page, Map<String, Object> map) {
		List<WdscVO> selectWdscPage = null;
		LoginUser currentUser = AuthUtil.getUser();
		if (currentUser == null) {
			return R.fail("未获取到当前登录用户信息");
		}
		//管理员账号可以查看全部收藏记录,非管理员仅可查看自己的收藏记录
		if (!isAdmin(currentUser.getRoleId())) {
			map.put("cjr",currentUser.getUserId());
		}
		map.put("offset",(page.getCurrent()-1)*page.getSize());
		selectWdscPage = baseMapper.selectWdscPage(page,map);
		for(WdscVO wdscVO : selectWdscPage){
			//当非外校资料类型时，查询对应节点名称
			if(FilesTypeEnum.SUB_FILES.getCode().equals(wdscVO.getWxzlLx())){
				//查询分类管理资料文件夹
				FlglWjj flglWjj = iFlglWjjService.getById(wdscVO.getWjjId());
				//文件夹非空时查询对应节点
				if(Func.isNotEmpty(flglWjj)){
					ZlkJdgl zlkJdgl = jdglService.getById(flglWjj.getJdId());
					if(Func.isNotEmpty(zlkJdgl)){
						//赋值节点名称
						wdscVO.setJdMc(zlkJdgl.getJdMc());
					}
				}
			}
		}
		return R.data(page.setRecords(selectWdscPage));
	}

	/**
	 * 判断角色是否为管理员
	 */
	private boolean isAdmin(String roleIds) {
		if (StringUtil.isBlank(roleIds)) {
			return false;
		}

		List<String> userRoles = Arrays.asList(roleIds.split(","));

		// 检查超级管理员
		if (StringUtil.isNotBlank(adminRoleIds)) {
			String[] adminRoleArray = adminRoleIds.split(",");
			for (String adminRole : adminRoleArray) {
				if (userRoles.contains(adminRole.trim())) {
					return true;
				}
			}
		}

		// 检查教评中心管理员
		if (StringUtil.isNotBlank(zlkGlyRoleId)) {
			String[] zpbgAdminRoleArray = zlkGlyRoleId.split(",");
			for (String adminRole : zpbgAdminRoleArray) {
				if (userRoles.contains(adminRole.trim())) {
					return true;
				}
			}
		}

		// 默认管理员判断逻辑（兜底）
		return isDefaultAdmin(roleIds);
	}

	/**
	 * 默认管理员判断逻辑（当配置未加载时使用）
	 */
	private static boolean isDefaultAdmin(String roleIds) {
		LoginUser user = AuthUtil.getUser();
		if (user != null) {
			// 检查特殊账号
			String account = user.getAccount();
			if ("admin".equals(account) || "admin_root".equals(account)) {
				return true;
			}
		}

		// 检查默认管理员角色ID
		List<String> userRoles = Arrays.asList(roleIds.split(","));
		return userRoles.contains("1") || userRoles.contains("admin");
	}
}
